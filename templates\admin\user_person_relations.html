{% extends "admin/base.html" %}

{% block title %}用户与人员关联管理{% endblock %}

{% block page_title %}用户与人员关联管理{% endblock %}

{% block page_actions %}
<button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createRelationModal">
    <i class="bi bi-plus-lg"></i> 创建关联
</button>
{% endblock %}

{% block content %}
<!-- 消息提示 -->
{% if request.query_params.get('success') %}
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ request.query_params.get('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{% endif %}
{% if request.query_params.get('error') %}
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ request.query_params.get('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{% endif %}

<!-- 关联列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">关联列表</h5>
    </div>
    <div class="card-body">
        {% if relations %}
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>人员姓名</th>
                        <th>工号</th>
                        <th>部门</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for relation in relations %}
                    <tr>
                        <td>{{ relation.id }}</td>
                        <td>{{ relation.username }}</td>
                        <td>{{ relation.person_name }}</td>
                        <td>{{ relation.employee_id if relation.employee_id else '-' }}</td>
                        <td>{{ relation.department_name if relation.department_name else '-' }}</td>
                        <td>{{ relation.created_at }}</td>
                        <td>
                            <button class="btn btn-sm btn-danger delete-relation"
                                    data-relation-id="{{ relation.id }}"
                                    data-username="{{ relation.username }}"
                                    data-person-name="{{ relation.person_name }}"
                                    title="删除关联">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <p class="text-muted">暂无关联记录</p>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createRelationModal">
                <i class="bi bi-plus-lg"></i> 创建第一个关联
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- 创建关联模态框 -->
<div class="modal fade" id="createRelationModal" tabindex="-1" aria-labelledby="createRelationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createRelationModalLabel">创建用户与人员关联</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createRelationForm" method="post" action="/admin/user-person-relations/create">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="user_id" class="form-label">选择用户</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">请选择用户账号</option>
                            {% for user in unrelated_users %}
                            <option value="{{ user.id }}">{{ user.username }}{% if user.role %} ({{ user.role }}){% endif %}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="person_id" class="form-label">选择人员</label>
                        <select class="form-select" id="person_id" name="person_id" required>
                            <option value="">请选择人员信息</option>
                            {% for person in unrelated_persons %}
                            <option value="{{ person.id }}">
                                {{ person.name }}{% if person.employee_id %} ({{ person.employee_id }}){% endif %}{% if person.department_name %} - {{ person.department_name }}{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    {% if not unrelated_users or not unrelated_persons %}
                    <div class="alert alert-warning">
                        {% if not unrelated_users and not unrelated_persons %}
                        当前没有可关联的用户和人员。
                        {% elif not unrelated_users %}
                        当前没有可关联的用户。
                        {% else %}
                        当前没有可关联的人员。
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success" {% if not unrelated_users or not unrelated_persons %}disabled{% endif %}>创建关联</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除用户 <span id="deleteUsername" class="fw-bold"></span> 与人员 <span id="deletePersonName" class="fw-bold"></span> 的关联吗？</p>
                <p class="text-danger">此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 初始化DataTable
        if (!$.fn.DataTable.isDataTable('.datatable')) {
            $('.datatable').DataTable({
                order: [[0, 'desc']] // 按ID降序排序
            });
        }

        // 创建关联表单提交验证
        $('#createRelationForm').on('submit', function(e) {
            const userId = $('#user_id').val();
            const personId = $('#person_id').val();

            if (!userId || !personId) {
                e.preventDefault();
                alert('请选择用户和人员');
                return false;
            }
        });

        // 删除关联 - 使用事件委托处理分页后的元素
        let deleteRelationId, deleteUsername, deletePersonName;

        $(document).on('click', '.delete-relation', function() {
            deleteRelationId = $(this).data('relation-id');
            deleteUsername = $(this).data('username');
            deletePersonName = $(this).data('person-name');

            // 更新模态框内容
            $('#deleteUsername').text(deleteUsername);
            $('#deletePersonName').text(deletePersonName);

            // 显示删除确认模态框
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });

        // 确认删除
        $('#confirmDelete').on('click', function() {
            if (!deleteRelationId) {
                alert('删除参数错误');
                return;
            }

            const btn = $(this);
            const originalText = btn.html();
            btn.html('删除中...').prop('disabled', true);

            $.ajax({
                url: `/admin/api/user-person-relations/${deleteRelationId}`,
                type: 'DELETE',
                success: function(response) {
                    // 隐藏模态框
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }

                    if (response.success) {
                        alert('关联删除成功');
                        window.location.reload();
                    } else {
                        alert('删除失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    // 隐藏模态框
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }

                    let errorMessage = '服务器错误，请稍后再试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    alert(errorMessage);
                },
                complete: function() {
                    btn.html(originalText).prop('disabled', false);
                }
            });
        });

        // 自动隐藏成功/错误消息
        setTimeout(function() {
            $('.alert-dismissible').fadeOut();
        }, 5000);
    });
</script>
{% endblock %}

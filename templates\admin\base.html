<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}人脸识别考勤系统 - 后台管理{% endblock %}</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #3f51b5;
            --secondary-color: #f50057;
            --sidebar-width: 250px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            overflow-x: hidden;
            font-size: 1.1rem; /* 增加基础字体大小 */
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 0.5rem 1rem;
            position: fixed;
            top: 0;
            right: 0;
            left: 0;
            z-index: 1030;
        }

        .navbar-brand {
            font-weight: 600;
            color: white;
        }

        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            width: var(--sidebar-width);
            padding: 1rem 0;
            background-color: white;
            box-shadow: 1px 0 10px rgba(0, 0, 0, 0.05);
            overflow-y: auto;
            transition: all 0.3s;
            z-index: 1020;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            border-left: 3px solid transparent;
            transition: all 0.2s;
        }

        .sidebar .nav-link:hover {
            color: var(--primary-color);
            background-color: #f8f9fa;
        }

        .sidebar .nav-link.active {
            color: var(--primary-color);
            border-left-color: var(--primary-color);
            background-color: rgba(63, 81, 181, 0.05);
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            margin-top: 56px;
            padding: 1.2rem;
            transition: all 0.3s;
        }

        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.2rem;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.25rem;
        }

        .card-title {
            margin-bottom: 0;
            font-weight: 600;
            color: #333;
            font-size: 1.4rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #303f9f;
            border-color: #303f9f;
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .btn-success {
            background-color: #4caf50;
            border-color: #4caf50;
        }

        .btn-danger {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-danger:hover {
            background-color: #c51162;
            border-color: #c51162;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            color: #333;
        }

        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            font-weight: 600;
            background-color: #f8f9fa;
            font-size: 1.15rem;
        }

        .table td {
            font-size: 1.1rem;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(63, 81, 181, 0.03);
        }

        .badge {
            font-weight: 500;
            padding: 0.35em 0.65em;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }

        /* DataTables Customization */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            color: #495057;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: var(--primary-color) !important;
            color: white !important;
            border-color: var(--primary-color) !important;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-person-badge-fill me-2"></i>
                人脸识别考勤系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/" target="_blank">
                            <i class="bi bi-house-door"></i> 前台首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/logout">
                            <i class="bi bi-box-arrow-right"></i> 退出登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'dashboard' %}active{% endif %}" href="/admin">
                    <i class="bi bi-speedometer2"></i> 仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'users' %}active{% endif %}" href="/admin/users">
                    <i class="bi bi-people"></i> 用户管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'departments' %}active{% endif %}" href="/admin/departments">
                    <i class="bi bi-diagram-3"></i> 部门管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'persons' %}active{% endif %}" href="/admin/persons">
                    <i class="bi bi-person-vcard"></i> 人员管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'attendance' %}active{% endif %}" href="/admin/attendance">
                    <i class="bi bi-calendar-check"></i> 考勤管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'attendance_rules' %}active{% endif %}" href="/admin/attendance-rules">
                    <i class="bi bi-clock"></i> 考勤规则
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'leave' %}active{% endif %}" href="/admin/leave">
                    <i class="bi bi-calendar-x"></i> 请假管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'reports' %}active{% endif %}" href="/admin/reports">
                    <i class="bi bi-bar-chart"></i> 统计报表
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'announcements' %}active{% endif %}" href="/admin/announcements">
                    <i class="bi bi-megaphone"></i> 通知公告
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_page == 'user_person_relations' %}active{% endif %}" href="/admin/user-person-relations">
                    <i class="bi bi-link"></i> 用户人员关联
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'settings' %}active{% endif %}" href="/admin/settings">
                    <i class="bi bi-gear"></i> 系统设置
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main content -->
    <main class="main-content">
        {% if error %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        {% if success %}
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ success }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        <div class="page-header">
            <h1 class="page-title">{% block page_title %}仪表盘{% endblock %}</h1>
            <div class="page-actions">
                {% block page_actions %}{% endblock %}
            </div>
        </div>

        {% block content %}{% endblock %}
    </main>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // DataTables 默认配置
        $.extend(true, $.fn.dataTable.defaults, {
            language: {
                "sProcessing": "处理中...",
                "sLengthMenu": "显示 _MENU_ 项结果",
                "sZeroRecords": "没有匹配结果",
                "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                "sSearch": "搜索:",
                "oPaginate": {
                    "sFirst": "首页",
                    "sPrevious": "上页",
                    "sNext": "下页",
                    "sLast": "末页"
                }
            },
            responsive: true
        });

        // Toggle sidebar on mobile
        document.querySelector('.navbar-toggler').addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('show');
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

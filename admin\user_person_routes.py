#!/usr/bin/env python3
import sqlite3
from fastapi import APIRouter, Request, Depends, HTTPException, Form
from fastapi.templating import Jinja2Templates
from fastapi.responses import JSONResponse, RedirectResponse
from admin.auth import get_current_admin_user
from admin.db_init import DATABASE
from admin.user_person_relation import (
    get_all_relations,
    get_unrelated_users,
    get_unrelated_persons,
    create_relation,
    delete_relation,
    get_all_users,
    get_all_persons
)

router = APIRouter(
    prefix="/admin",
    tags=["admin", "user-person-relations"],
)

templates = Jinja2Templates(directory="templates")

@router.get("/user-person-relations")
async def user_person_relations_page(request: Request, username: str = Depends(get_current_admin_user)):
    """用户与人员关联管理页面"""
    try:
        relations = get_all_relations()
        all_users = get_all_users()
        all_persons = get_all_persons()
        unrelated_users = get_unrelated_users()
        unrelated_persons = get_unrelated_persons()

        return templates.TemplateResponse("admin/user_person_relations.html", {
            "request": request,
            "username": username,
            "current_page": "user_person_relations",
            "relations": relations,
            "all_users": all_users,
            "all_persons": all_persons,
            "unrelated_users": unrelated_users,
            "unrelated_persons": unrelated_persons,
            "total_relations": len(relations),
            "total_unrelated_users": len(unrelated_users),
            "total_unrelated_persons": len(unrelated_persons)
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取页面数据失败: {str(e)}")

@router.post("/user-person-relations/create")
async def create_user_person_relation_form(
    request: Request,
    username: str = Depends(get_current_admin_user),
    user_id: int = Form(...),
    person_id: int = Form(...)
):
    """通过表单创建用户与人员的关联关系"""
    try:
        result = create_relation(user_id, person_id)
        if result["success"]:
            return RedirectResponse(
                url="/admin/user-person-relations?success=" + result["message"],
                status_code=303
            )
        else:
            return RedirectResponse(
                url="/admin/user-person-relations?error=" + result["message"],
                status_code=303
            )
    except Exception as e:
        return RedirectResponse(
            url="/admin/user-person-relations?error=创建关联时发生错误",
            status_code=303
        )

@router.post("/api/user-person-relations")
async def create_user_person_relation_api(request: Request, username: str = Depends(get_current_admin_user)):
    """通过API创建用户与人员的关联关系"""
    try:
        data = await request.json()
        user_id = data.get("user_id")
        person_id = data.get("person_id")

        # 数据验证
        if not user_id or not person_id:
            return JSONResponse({
                "success": False,
                "message": "用户ID和人员ID不能为空"
            }, status_code=400)

        if not isinstance(user_id, int) or not isinstance(person_id, int):
            return JSONResponse({
                "success": False,
                "message": "用户ID和人员ID必须是有效的数字"
            }, status_code=400)

        result = create_relation(user_id, person_id)
        status_code = 200 if result["success"] else 400
        return JSONResponse(result, status_code=status_code)

    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"服务器错误: {str(e)}"
        }, status_code=500)

@router.delete("/api/user-person-relations/{relation_id}")
async def delete_user_person_relation_by_id(
    relation_id: int,
    username: str = Depends(get_current_admin_user)
):
    """通过关联ID删除用户与人员的关联关系"""
    try:
        # 先获取关联信息
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        c.execute("SELECT user_id, person_id FROM user_person_relations WHERE id = ?", (relation_id,))
        relation = c.fetchone()
        conn.close()

        if not relation:
            return JSONResponse({
                "success": False,
                "message": "未找到指定的关联关系"
            }, status_code=404)

        result = delete_relation(relation["user_id"], relation["person_id"])
        status_code = 200 if result["success"] else 400
        return JSONResponse(result, status_code=status_code)

    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"服务器错误: {str(e)}"
        }, status_code=500)

@router.post("/api/user-person-relations/delete")
async def delete_user_person_relation_legacy(request: Request, username: str = Depends(get_current_admin_user)):
    """删除用户与人员的关联关系（兼容旧API）"""
    try:
        data = await request.json()
        user_id = data.get("user_id")
        person_id = data.get("person_id")

        if not user_id or not person_id:
            return JSONResponse({
                "success": False,
                "message": "用户ID和人员ID不能为空"
            }, status_code=400)

        result = delete_relation(user_id, person_id)
        status_code = 200 if result["success"] else 400
        return JSONResponse(result, status_code=status_code)

    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"服务器错误: {str(e)}"
        }, status_code=500)

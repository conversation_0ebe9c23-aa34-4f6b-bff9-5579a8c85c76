{% extends "admin/base.html" %}

{% block title %}统计报表{% endblock %}

{% block page_title %}统计报表{% endblock %}

{% block page_actions %}
<div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
        <a href="/admin/export/attendance?start_date={{ start_date }}&end_date={{ end_date }}" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-file-earmark-excel"></i> 导出Excel
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">考勤数据筛选</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="department_id" class="form-label">部门</label>
                        <select class="form-select" id="department_id" name="department_id">
                            <option value="">全部部门</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if selected_department == dept.id %}selected{% endif %}>
                                {{ dept.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">查询</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">部门考勤统计</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="dept-stats-table" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>部门</th>
                                <th>人数</th>
                                <th>正常</th>
                                <th>迟到</th>
                                <th>早退</th>
                                <th>缺勤</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dept in dept_stats %}
                            <tr>
                                <td>{{ dept.department_name }}</td>
                                <td>{{ dept.person_count }}</td>
                                <td>{{ dept.normal_count }}</td>
                                <td>{{ dept.late_count }}</td>
                                <td>{{ dept.early_count }}</td>
                                <td>{{ dept.absent_count }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center">暂无数据</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">考勤状态分布</h5>
            </div>
            <div class="card-body">
                <div style="max-width: 300px; margin: 0 auto;">
                    <canvas id="statusChart" width="300" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 个人考勤统计部分已移除 -->
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化DataTable
    if (!$.fn.DataTable.isDataTable('#dept-stats-table')) {
        $('#dept-stats-table').DataTable({
            responsive: true,
            paging: false,
            searching: false,
            info: false
        });
    }

    // 个人考勤统计表格已移除

    // 如果有部门统计数据，绘制饼图
    {% if dept_stats %}
    const ctx = document.getElementById('statusChart').getContext('2d');

    // 计算各状态总数
    let normalTotal = 0;
    let lateTotal = 0;
    let earlyTotal = 0;
    let absentTotal = 0;

    {% for dept in dept_stats %}
    normalTotal += {{ dept.normal_count }};
    lateTotal += {{ dept.late_count }};
    earlyTotal += {{ dept.early_count }};
    absentTotal += {{ dept.absent_count }};
    {% endfor %}

    const statusChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['正常', '迟到', '早退', '缺勤'],
            datasets: [{
                data: [normalTotal, lateTotal, earlyTotal, absentTotal],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(255, 159, 64, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                title: {
                    display: true,
                    text: '考勤状态分布'
                }
            }
        }
    });
    {% endif %}
});
</script>
{% endblock %}

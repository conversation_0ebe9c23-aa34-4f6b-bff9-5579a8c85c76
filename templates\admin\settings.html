{% extends "admin/base.html" %}

{% block title %}系统设置{% endblock %}

{% block page_title %}系统设置{% endblock %}

{% block content %}
{% if success %}
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ success }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
{% endif %}

{% if error %}
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ error }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
{% endif %}

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">系统设置</h5>
    </div>
    <div class="card-body">
        <form method="post" action="/admin/settings">
            <div class="row">
                {% for setting in settings %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">{{ setting.description }}</h6>
                        </div>
                        <div class="card-body">
                            {% if setting.key == 'face_recognition_threshold' %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}" class="form-label">人脸识别阈值 (0.0-1.0)</label>
                                <input type="range" class="form-range" min="0" max="1" step="0.01" 
                                       id="setting_{{ setting.key }}" name="setting_{{ setting.key }}" 
                                       value="{{ setting.value }}">
                                <div class="text-center" id="threshold_value">{{ setting.value }}</div>
                            </div>
                            {% elif setting.key == 'attendance_time_range' %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}" class="form-label">考勤时间范围 (分钟)</label>
                                <input type="number" class="form-control" id="setting_{{ setting.key }}" 
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                <div class="form-text">允许的考勤时间范围，超出此范围将被标记为异常</div>
                            </div>
                            {% elif setting.key == 'enable_email_notification' %}
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="setting_{{ setting.key }}"
                                           name="setting_{{ setting.key }}" value="1"
                                           {% if setting.value == '1' %}checked{% endif %}>
                                    <label class="form-check-label" for="setting_{{ setting.key }}">启用邮件通知</label>
                                </div>
                            </div>
                            {% elif setting.key == 'disable_time_check' %}
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="setting_{{ setting.key }}"
                                           name="setting_{{ setting.key }}" value="true"
                                           {% if setting.value == 'true' %}checked{% endif %}>
                                    <label class="form-check-label" for="setting_{{ setting.key }}">关闭考勤时间检测</label>
                                </div>
                                <div class="form-text text-warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    开启后，人脸识别签到将不受考勤规则时间限制，任何时间都可以签到
                                </div>
                            </div>
                            {% elif setting.key == 'smtp_settings' %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}" class="form-label">SMTP 设置 (JSON)</label>
                                <textarea class="form-control" id="setting_{{ setting.key }}" 
                                          name="setting_{{ setting.key }}" rows="4">{{ setting.value }}</textarea>
                                <div class="form-text">格式: {"server": "smtp.example.com", "port": 587, "username": "user", "password": "pass"}</div>
                            </div>
                            {% elif setting.key == 'system_logo' %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}" class="form-label">系统 Logo URL</label>
                                <input type="text" class="form-control" id="setting_{{ setting.key }}" 
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                {% if setting.value %}
                                <div class="mt-2">
                                    <img src="{{ setting.value }}" alt="系统Logo" style="max-height: 50px;">
                                </div>
                                {% endif %}
                            </div>
                            {% elif setting.key == 'system_name' %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}" class="form-label">系统名称</label>
                                <input type="text" class="form-control" id="setting_{{ setting.key }}" 
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                            </div>
                            {% elif setting.key == 'data_retention_days' %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}" class="form-label">数据保留天数</label>
                                <input type="number" class="form-control" id="setting_{{ setting.key }}" 
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                <div class="form-text">超过此天数的考勤数据将被自动归档</div>
                            </div>
                            {% else %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}" class="form-label">{{ setting.description }}</label>
                                <input type="text" class="form-control" id="setting_{{ setting.key }}" 
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="col-12">
                    <div class="alert alert-info">
                        没有找到系统设置项。请先在数据库中添加设置项。
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">保存设置</button>
                <button type="reset" class="btn btn-secondary">重置</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 人脸识别阈值滑块实时显示值
    const thresholdSlider = document.getElementById('setting_face_recognition_threshold');
    const thresholdValue = document.getElementById('threshold_value');
    
    if (thresholdSlider && thresholdValue) {
        thresholdSlider.addEventListener('input', function() {
            thresholdValue.textContent = this.value;
        });
    }
    
    // SMTP设置JSON格式化
    const smtpSettings = document.getElementById('setting_smtp_settings');
    if (smtpSettings) {
        try {
            const settings = JSON.parse(smtpSettings.value);
            smtpSettings.value = JSON.stringify(settings, null, 2);
        } catch (e) {
            // 如果不是有效的JSON，保持原样
        }
    }
});
</script>
{% endblock %}

#!/usr/bin/env python3
import sqlite3
from datetime import datetime, timedelta

# 数据库路径
DATABASE = "face_recognition.db"

def add_test_announcements():
    """添加测试公告"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    # 检查announcements表是否存在
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='announcements'")
    if not c.fetchone():
        print("创建announcements表...")
        c.execute('''
            CREATE TABLE announcements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                type TEXT DEFAULT 'normal',
                priority INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active',
                start_date TEXT,
                end_date TEXT,
                created_by INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("announcements表创建成功")
    
    # 清空现有公告
    c.execute("DELETE FROM announcements")
    print("已清空现有公告")
    
    # 获取当前日期
    now = datetime.now()
    today = now.strftime("%Y-%m-%d")
    
    # 添加测试公告
    announcements = [
        {
            "title": "系统维护通知",
            "content": "尊敬的用户，系统将于本周六晚上22:00-24:00进行例行维护，期间系统将不可用。请提前做好工作安排，谢谢配合！",
            "type": "important",
            "priority": 2,
            "status": "active",
            "start_date": today,
            "end_date": (now + timedelta(days=7)).strftime("%Y-%m-%d"),
            "created_at": now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "title": "五一劳动节放假安排",
            "content": "根据国家法定节假日安排，公司五一劳动节放假时间为5月1日至5月5日，共5天。4月28日（周日）正常上班。祝大家节日快乐！",
            "type": "normal",
            "priority": 1,
            "status": "active",
            "start_date": today,
            "end_date": (now + timedelta(days=14)).strftime("%Y-%m-%d"),
            "created_at": now.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "title": "新版考勤系统上线通知",
            "content": "新版考勤系统已于今日正式上线，新系统增加了移动端打卡、请假申请等功能。请各位同事尽快熟悉新系统的使用方法。如有问题，请联系IT部门。",
            "type": "normal",
            "priority": 0,
            "status": "active",
            "start_date": today,
            "end_date": (now + timedelta(days=30)).strftime("%Y-%m-%d"),
            "created_at": now.strftime("%Y-%m-%d %H:%M:%S")
        }
    ]
    
    for announcement in announcements:
        c.execute("""
            INSERT INTO announcements 
            (title, content, type, priority, status, start_date, end_date, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            announcement["title"],
            announcement["content"],
            announcement["type"],
            announcement["priority"],
            announcement["status"],
            announcement["start_date"],
            announcement["end_date"],
            announcement["created_at"],
            announcement["created_at"]
        ))
    
    conn.commit()
    print(f"已添加 {len(announcements)} 条测试公告")
    
    # 验证添加结果
    c.execute("SELECT id, title, type, priority, status FROM announcements")
    results = c.fetchall()
    
    print("\n已添加的公告:")
    for result in results:
        print(f"ID: {result['id']}, 标题: {result['title']}, 类型: {result['type']}, 优先级: {result['priority']}, 状态: {result['status']}")
    
    conn.close()

if __name__ == "__main__":
    add_test_announcements()

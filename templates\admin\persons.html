{% extends "admin/base.html" %}

{% block title %}人员管理{% endblock %}

{% block page_title %}人员管理{% endblock %}

{% block page_actions %}
<a href="/admin/persons/new" class="btn btn-success">
    <i class="bi bi-plus-lg"></i> 新增人员
</a>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">人员列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>部门</th>
                        <th>职位</th>
                        <th>工号</th>
                        <th>性别</th>
                        <th>人脸照片</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for person in persons %}
                    <tr>
                        <td>{{ person.id }}</td>
                        <td>{{ person.name }}</td>
                        <td>{{ person.department_name if person.department_name else '未分配' }}</td>
                        <td>{{ person.position if person.position else '-' }}</td>
                        <td>{{ person.employee_id if person.employee_id else '-' }}</td>
                        <td>{{ person.gender if person.gender else '-' }}</td>
                        <td>
                            {% if person.face_image_path %}
                            <a href="/{{ person.face_image_path }}" target="_blank" class="btn btn-sm btn-outline-info">
                                <i class="bi bi-image"></i> 查看
                            </a>
                            {% else %}
                            <span class="badge bg-secondary">未录入</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="/admin/persons/{{ person.id }}/edit" class="btn btn-sm btn-primary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="/admin/persons/{{ person.id }}/face" class="btn btn-sm btn-info" title="人脸录入">
                                    <i class="bi bi-camera"></i>
                                </a>
                                <a href="/admin/persons/{{ person.id }}/rules" class="btn btn-sm btn-warning" title="考勤规则">
                                    <i class="bi bi-clock"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ person.id }}" data-name="{{ person.name }}" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除人员 <span id="personName" class="fw-bold"></span> 吗？</p>
                <p class="text-danger">此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 初始化DataTable
        if (!$.fn.DataTable.isDataTable('.datatable')) {
            $('.datatable').DataTable();
        }

        // 删除确认 - 使用事件委托处理分页后的元素
        $(document).on('click', '.delete-btn', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            $('#personName').text(name);
            $('#deleteForm').attr('action', `/admin/persons/${id}/delete`);

            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
    });
</script>
{% endblock %}
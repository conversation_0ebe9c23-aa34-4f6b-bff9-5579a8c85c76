#!/usr/bin/env python3
import os
import sqlite3
from passlib.context import CryptContext

# 数据库配置
DATABASE = 'face_recognition.db'
pwd_context = CryptContext(schemes=["sha256_crypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def init_db():
    """初始化基本数据库表"""
    # 增强数据库存在性检查
    db_exists = os.path.exists(DATABASE)
    conn = sqlite3.connect(DATABASE, detect_types=sqlite3.PARSE_DECLTYPES, isolation_level=None)
    conn.execute('PRAGMA encoding = "UTF-8"')
    c = conn.cursor()

    c.execute('''CREATE TABLE IF NOT EXISTS users
                (id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL)''')

    c.execute('''CREATE TABLE IF NOT EXISTS persons
                (id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                gender TEXT,
                position TEXT,
                face_image_path TEXT UNIQUE,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP)''')

    c.execute('''CREATE TABLE IF NOT EXISTS attendance
                (id INTEGER PRIMARY KEY AUTOINCREMENT,
                person_id INTEGER NOT NULL,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(person_id) REFERENCES persons(id))''')

    admin_username = 'admin'
    admin_password = 'admin'
    c.execute("SELECT id FROM users WHERE username=?", (admin_username,))
    if c.fetchone() is None:
        c.execute("DELETE FROM users WHERE username=?", (admin_username,))
        admin_hash = get_password_hash(admin_password)
        c.execute("INSERT INTO users (username, password_hash) VALUES (?, ?)",
                (admin_username, admin_hash))
    conn.commit()
    conn.close()

def init_admin_db():
    """初始化管理后台所需的数据库表"""
    # 自动创建数据库目录（仅在需要时）
    db_dir = os.path.dirname(DATABASE)
    if db_dir:
        os.makedirs(db_dir, exist_ok=True)
    conn = sqlite3.connect(DATABASE, detect_types=sqlite3.PARSE_DECLTYPES, isolation_level=None)
    conn.execute('PRAGMA encoding = "UTF-8"')
    conn.execute('PRAGMA foreign_keys = ON')  # 启用外键约束
    c = conn.cursor()

    # 创建部门表
    c.execute('''CREATE TABLE IF NOT EXISTS departments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                parent_id INTEGER,
                manager_id INTEGER,
                description TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(parent_id) REFERENCES departments(id),
                FOREIGN KEY(manager_id) REFERENCES persons(id))''')

    # 更新部门表，添加status列
    c.execute("PRAGMA table_info(departments)")
    columns = [column[1] for column in c.fetchall()]

    if 'status' not in columns:
        c.execute("ALTER TABLE departments ADD COLUMN status INTEGER DEFAULT 1")

    # 更新人员表，添加部门关联
    c.execute("PRAGMA table_info(persons)")
    columns = [column[1] for column in c.fetchall()]

    if 'department_id' not in columns:
        c.execute("ALTER TABLE persons ADD COLUMN department_id INTEGER REFERENCES departments(id)")

    if 'employee_id' not in columns:
        # 移除了UNIQUE约束以避免现有数据冲突
        c.execute("ALTER TABLE persons ADD COLUMN employee_id TEXT")

    if 'status' not in columns:
        c.execute("ALTER TABLE persons ADD COLUMN status TEXT DEFAULT 'active'")

    # 更新用户表，添加角色和邮箱
    c.execute("PRAGMA table_info(users)")
    columns = [column[1] for column in c.fetchall()]

    if 'role' not in columns:
        c.execute("ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'admin'")

    if 'email' not in columns:
        c.execute("ALTER TABLE users ADD COLUMN email TEXT")

    if 'created_at' not in columns:
        c.execute("ALTER TABLE users ADD COLUMN created_at TEXT")

    if 'last_login' not in columns:
        c.execute("ALTER TABLE users ADD COLUMN last_login TEXT")

    # 更新考勤表，添加类型和状态
    c.execute("PRAGMA table_info(attendance)")
    columns = [column[1] for column in c.fetchall()]

    if 'type' not in columns:
        c.execute("ALTER TABLE attendance ADD COLUMN type TEXT DEFAULT 'auto'")

    if 'status' not in columns:
        c.execute("ALTER TABLE attendance ADD COLUMN status TEXT DEFAULT 'normal'")

    if 'note' not in columns:
        c.execute("ALTER TABLE attendance ADD COLUMN note TEXT")

    if 'created_by' not in columns:
        c.execute("ALTER TABLE attendance ADD COLUMN created_by INTEGER REFERENCES users(id)")

    if 'check_type' not in columns:
        c.execute("ALTER TABLE attendance ADD COLUMN check_type TEXT DEFAULT 'unknown'")

    # 创建班次表
    c.execute('''CREATE TABLE IF NOT EXISTS shifts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                start_time TEXT NOT NULL,
                end_time TEXT NOT NULL,
                late_threshold INTEGER DEFAULT 15,
                early_leave_threshold INTEGER DEFAULT 15,
                description TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP)''')

    # 创建考勤规则表
    c.execute('''CREATE TABLE IF NOT EXISTS attendance_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                check_in_time TEXT NOT NULL,
                check_out_time TEXT NOT NULL,
                late_threshold INTEGER DEFAULT 15,
                early_leave_threshold INTEGER DEFAULT 15,
                work_days TEXT DEFAULT '1,2,3,4,5',
                location_required BOOLEAN DEFAULT 0,
                location_range INTEGER DEFAULT 100,
                latitude REAL,
                longitude REAL,
                is_default BOOLEAN DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP)''')

    # 创建部门考勤规则关联表
    c.execute('''CREATE TABLE IF NOT EXISTS department_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                department_id INTEGER NOT NULL,
                rule_id INTEGER NOT NULL,
                effective_from TEXT NOT NULL,
                effective_to TEXT,
                FOREIGN KEY(department_id) REFERENCES departments(id),
                FOREIGN KEY(rule_id) REFERENCES attendance_rules(id))''')

    # 创建个人考勤规则关联表
    c.execute('''CREATE TABLE IF NOT EXISTS person_attendance_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                person_id INTEGER NOT NULL,
                rule_id INTEGER NOT NULL,
                effective_date TEXT NOT NULL,
                expiry_date TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(person_id) REFERENCES persons(id),
                FOREIGN KEY(rule_id) REFERENCES attendance_rules(id))''')

    # 创建假期类型表
    c.execute('''CREATE TABLE IF NOT EXISTS leave_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                paid BOOLEAN DEFAULT 1,
                color TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP)''')

    # 创建假期申请表
    c.execute('''CREATE TABLE IF NOT EXISTS leave_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                person_id INTEGER NOT NULL,
                leave_type_id INTEGER NOT NULL,
                start_date TEXT NOT NULL,
                end_date TEXT NOT NULL,
                start_half INTEGER DEFAULT 0,
                end_half INTEGER DEFAULT 0,
                reason TEXT,
                status TEXT DEFAULT 'pending',
                approver_id INTEGER,
                approved_at TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(person_id) REFERENCES persons(id),
                FOREIGN KEY(leave_type_id) REFERENCES leave_types(id),
                FOREIGN KEY(approver_id) REFERENCES users(id))''')

    # 创建系统设置表
    c.execute('''CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT NOT NULL UNIQUE,
                value TEXT,
                description TEXT,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP)''')

    # 创建用户角色表
    c.execute('''CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                permissions TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP)''')

    # 创建通知公告表
    c.execute('''CREATE TABLE IF NOT EXISTS announcements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                type TEXT DEFAULT 'normal',
                status TEXT DEFAULT 'active',
                priority INTEGER DEFAULT 0,
                start_date TEXT,
                end_date TEXT,
                created_by INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(created_by) REFERENCES users(id))''')

    # 插入初始数据
    # 默认班次
    c.execute("SELECT COUNT(*) FROM shifts")
    if c.fetchone()[0] == 0:
        c.execute("INSERT INTO shifts (name, start_time, end_time, description) VALUES (?, ?, ?, ?)",
                 ("标准班", "09:00", "18:00", "标准8小时工作制"))
        c.execute("INSERT INTO shifts (name, start_time, end_time, description) VALUES (?, ?, ?, ?)",
                 ("早班", "08:00", "17:00", "早班8小时工作制"))
        c.execute("INSERT INTO shifts (name, start_time, end_time, description) VALUES (?, ?, ?, ?)",
                 ("晚班", "13:00", "22:00", "晚班8小时工作制"))

    # 默认考勤规则
    c.execute("SELECT COUNT(*) FROM attendance_rules")
    if c.fetchone()[0] == 0:
        # 标准工作日规则
        c.execute("""
            INSERT INTO attendance_rules (
                name, description, check_in_time, check_out_time,
                late_threshold, early_leave_threshold, work_days,
                location_required, is_default
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "标准工作日",
            "周一至周五工作，周末休息",
            "09:00", "18:00",
            15, 15,
            "1,2,3,4,5",
            0, 1
        ))

        # 早班规则
        c.execute("""
            INSERT INTO attendance_rules (
                name, description, check_in_time, check_out_time,
                late_threshold, early_leave_threshold, work_days,
                location_required, is_default
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "早班工作日",
            "周一至周五早班，周末休息",
            "08:00", "17:00",
            15, 15,
            "1,2,3,4,5",
            0, 0
        ))

        # 晚班规则
        c.execute("""
            INSERT INTO attendance_rules (
                name, description, check_in_time, check_out_time,
                late_threshold, early_leave_threshold, work_days,
                location_required, is_default
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "晚班工作日",
            "周一至周五晚班，周末休息",
            "13:00", "22:00",
            15, 15,
            "1,2,3,4,5",
            0, 0
        ))

    # 默认假期类型
    c.execute("SELECT COUNT(*) FROM leave_types")
    if c.fetchone()[0] == 0:
        leave_types = [
            ('年假', '法定带薪年假', 1, '#4CAF50'),
            ('病假', '因病请假', 1, '#F44336'),
            ('事假', '因私事请假', 0, '#2196F3'),
            ('婚假', '结婚休假', 1, '#9C27B0'),
            ('产假', '女职工产假', 1, '#E91E63'),
            ('陪产假', '男职工陪产假', 1, '#673AB7'),
            ('丧假', '直系亲属丧事', 1, '#607D8B')
        ]
        for lt in leave_types:
            c.execute("INSERT INTO leave_types (name, description, paid, color) VALUES (?, ?, ?, ?)", lt)

    # 默认角色
    c.execute("SELECT COUNT(*) FROM roles")
    if c.fetchone()[0] == 0:
        roles = [
            ('超级管理员', '系统最高权限', '{"all": true}'),
            ('人事管理员', '管理人员和考勤', '{"users": true, "attendance": true, "reports": true}'),
            ('部门主管', '管理部门员工和审批', '{"department": true, "approval": true}'),
            ('普通员工', '基本自助功能', '{"self": true}')
        ]
        for role in roles:
            c.execute("INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)", role)

    # 默认系统设置
    c.execute("SELECT COUNT(*) FROM settings")
    if c.fetchone()[0] == 0:
        settings = [
            ('company_name', '公司名称', '公司或组织名称'),
            ('attendance_start_day', '1', '每周开始日 (0-6, 0为周日)'),
            ('face_recognition_threshold', '0.4', '人脸识别匹配阈值'),
            ('notification_enabled', 'true', '是否启用系统通知'),
            ('auto_approve_overtime', 'false', '是否自动批准加班申请'),
            ('max_continuous_work_days', '6', '最大连续工作天数')
        ]
        for setting in settings:
            c.execute("INSERT INTO settings (key, value, description) VALUES (?, ?, ?)", setting)

    # 创建默认部门
    c.execute("SELECT COUNT(*) FROM departments")
    if c.fetchone()[0] == 0:
        c.execute("INSERT INTO departments (name, description) VALUES (?, ?)",
                 ("总部", "公司总部"))

    conn.commit()
    conn.close()
    print("管理后台数据库初始化完成")

def init_user_person_relation_table():
    """初始化用户与人员关联表"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()

    # 创建用户与人员关联表
    c.execute('''CREATE TABLE IF NOT EXISTS user_person_relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                person_id INTEGER NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(user_id) REFERENCES users(id),
                FOREIGN KEY(person_id) REFERENCES persons(id),
                UNIQUE(user_id, person_id))''')

    conn.commit()
    conn.close()
    print("用户与人员关联表初始化完成")

def init_all_db():
    """初始化所有数据库表"""
    init_db()
    init_admin_db()
    init_user_person_relation_table()
    print("所有数据库表初始化完成")

if __name__ == "__main__":
    init_all_db()

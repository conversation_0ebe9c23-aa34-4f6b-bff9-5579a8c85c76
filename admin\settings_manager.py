#!/usr/bin/env python3
"""
系统设置管理模块
"""
import sqlite3
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from admin.db_init import DATABASE

def get_setting(key, default_value=None):
    """
    获取系统设置值
    
    Args:
        key (str): 设置键名
        default_value: 默认值，如果设置不存在则返回此值
    
    Returns:
        设置值或默认值
    """
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("SELECT value FROM settings WHERE key = ?", (key,))
        result = c.fetchone()
        
        if result:
            return result[0]
        else:
            return default_value
    except Exception as e:
        print(f"获取设置 {key} 时出错: {str(e)}")
        return default_value
    finally:
        conn.close()

def get_setting_bool(key, default_value=False):
    """
    获取布尔类型的系统设置值
    
    Args:
        key (str): 设置键名
        default_value (bool): 默认值
    
    Returns:
        bool: 设置值
    """
    value = get_setting(key, str(default_value).lower())
    
    # 处理各种可能的真值表示
    if isinstance(value, str):
        return value.lower() in ['true', '1', 'yes', 'on', 'enabled']
    elif isinstance(value, bool):
        return value
    elif isinstance(value, int):
        return value != 0
    else:
        return default_value

def set_setting(key, value, description=None):
    """
    设置系统设置值
    
    Args:
        key (str): 设置键名
        value: 设置值
        description (str): 设置描述
    
    Returns:
        bool: 是否设置成功
    """
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        
        # 检查设置是否已存在
        c.execute("SELECT id FROM settings WHERE key = ?", (key,))
        existing = c.fetchone()
        
        if existing:
            # 更新现有设置
            c.execute("""
                UPDATE settings 
                SET value = ?, updated_at = datetime('now', 'localtime')
                WHERE key = ?
            """, (str(value), key))
        else:
            # 创建新设置
            c.execute("""
                INSERT INTO settings (key, value, description, updated_at)
                VALUES (?, ?, ?, datetime('now', 'localtime'))
            """, (key, str(value), description or key))
        
        conn.commit()
        return True
    except Exception as e:
        conn.rollback()
        print(f"设置 {key} 时出错: {str(e)}")
        return False
    finally:
        conn.close()

def get_all_settings():
    """
    获取所有系统设置
    
    Returns:
        dict: 所有设置的字典
    """
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("SELECT key, value FROM settings")
        results = c.fetchall()
        
        return {key: value for key, value in results}
    except Exception as e:
        print(f"获取所有设置时出错: {str(e)}")
        return {}
    finally:
        conn.close()

def is_time_check_disabled():
    """
    检查是否禁用了考勤时间检测
    
    Returns:
        bool: True表示禁用时间检测，False表示启用时间检测
    """
    return get_setting_bool('disable_time_check', False)

# 常用设置的快捷函数
def get_face_recognition_threshold():
    """获取人脸识别阈值"""
    return float(get_setting('face_recognition_threshold', '0.4'))

def get_company_name():
    """获取公司名称"""
    return get_setting('company_name', '人脸识别考勤系统')

def is_notification_enabled():
    """检查是否启用通知"""
    return get_setting_bool('notification_enabled', True)

def is_email_notification_enabled():
    """检查是否启用邮件通知"""
    return get_setting_bool('enable_email_notification', False)

if __name__ == "__main__":
    # 测试设置管理功能
    print("测试设置管理功能:")
    print(f"考勤时间检测是否禁用: {is_time_check_disabled()}")
    print(f"人脸识别阈值: {get_face_recognition_threshold()}")
    print(f"公司名称: {get_company_name()}")
    print(f"通知是否启用: {is_notification_enabled()}")
    print(f"邮件通知是否启用: {is_email_notification_enabled()}")
    
    print("\n所有设置:")
    settings = get_all_settings()
    for key, value in settings.items():
        print(f"  {key}: {value}")

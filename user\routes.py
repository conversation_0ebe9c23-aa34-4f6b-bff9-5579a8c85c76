#!/usr/bin/env python3
import sqlite3
from datetime import datetime, timedelta
from typing import Optional

from fastapi import APIRouter, Request, Depends
from fastapi.templating import Jinja2Templates

from admin.db_init import DATABASE
from user.auth import get_current_user

router = APIRouter(
    prefix="/user",
    tags=["user"],
    responses={404: {"description": "Not found"}},
)

templates = Jinja2Templates(directory="templates")

# 获取用户关联的人员信息
async def get_user_person(username: str):
    """
    获取用户关联的人员信息
    """
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    try:
        # 获取用户ID
        c.execute("SELECT id FROM users WHERE username = ?", (username,))
        user_result = c.fetchone()
        user_id = user_result['id'] if user_result else None

        # 获取用户关联的人员信息
        if user_id:
            c.execute("""
                SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name, d.id as department_id
                FROM persons p
                LEFT JOIN departments d ON p.department_id = d.id
                JOIN user_person_relations upr ON p.id = upr.person_id
                WHERE upr.user_id = ?
            """, (user_id,))
            person = c.fetchone()
        else:
            person = None

        # 如果找不到关联的人员信息，尝试通过用户名查找
        if not person:
            c.execute("""
                SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name, d.id as department_id
                FROM persons p
                LEFT JOIN departments d ON p.department_id = d.id
                WHERE p.name = ? OR p.employee_id = ?
            """, (username, username))
            person = c.fetchone()

        # 如果还是找不到，尝试模糊匹配
        if not person:
            c.execute("""
                SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name, d.id as department_id
                FROM persons p
                LEFT JOIN departments d ON p.department_id = d.id
                WHERE p.name LIKE ? OR p.employee_id LIKE ?
            """, (f"%{username}%", f"%{username}%"))
            person = c.fetchone()

        return person
    finally:
        conn.close()



# 调试用户角色
@router.get("/debug-role/{username}")
async def debug_user_role(username: str):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("SELECT username, role FROM users WHERE username=?", (username,))
        result = c.fetchone()
        if result:
            return {"username": result[0], "role": result[1]}
        return {"username": username, "role": "not found"}
    finally:
        conn.close()

# 用户仪表盘
@router.get("/")
async def user_dashboard(request: Request, username: str = Depends(get_current_user)):
    """
    用户仪表盘首页
    """
    # 不再需要检查用户角色，因为 get_current_user 函数已经做了检查
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取用户信息
    c.execute("SELECT id, username, role, email FROM users WHERE username=?", (username,))
    user = c.fetchone()

    # 获取用户ID
    c.execute("SELECT id FROM users WHERE username = ?", (username,))
    user_result = c.fetchone()
    user_id = user_result[0] if user_result else None

    # 获取用户关联的人员信息
    if user_id:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            JOIN user_person_relations upr ON p.id = upr.person_id
            WHERE upr.user_id = ?
        """, (user_id,))
        person = c.fetchone()
    else:
        person = None

    # 如果找不到关联的人员信息，尝试通过用户名查找
    if not person:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE p.name = ? OR p.employee_id = ?
        """, (username, username))
        person = c.fetchone()

    # 如果还是找不到，尝试模糊匹配
    if not person:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE p.name LIKE ? OR p.employee_id LIKE ?
        """, (f"%{username}%", f"%{username}%"))
        person = c.fetchone()

    person_id = person['id'] if person else None

    # 获取最近的考勤记录
    recent_records = []
    if person_id:
        c.execute("""
            SELECT a.id, a.timestamp, a.check_type, a.status
            FROM attendance a
            WHERE a.person_id = ?
            ORDER BY a.timestamp DESC
            LIMIT 5
        """, (person_id,))
        recent_records = c.fetchall()

    # 获取最近的公告 - 修改查询以确保正确获取公告
    c.execute("""
        SELECT a.id, a.title, a.content, a.type, a.priority, a.created_at
        FROM announcements a
        WHERE a.status = 'active'
        ORDER BY a.priority DESC, a.created_at DESC
        LIMIT 5
    """)
    recent_announcements = c.fetchall()

    # 获取考勤统计
    attendance_stats = {}
    if person_id:
        # 本月统计
        month_start = datetime.now().replace(day=1).strftime("%Y-%m-%d")
        month_end = datetime.now().strftime("%Y-%m-%d")

        # 正常出勤天数
        c.execute("""
            SELECT COUNT(DISTINCT date) as count
            FROM attendance
            WHERE person_id = ? AND status = 'normal'
            AND date BETWEEN ? AND ?
        """, (person_id, month_start, month_end))
        normal_days = c.fetchone()['count']

        # 迟到次数
        c.execute("""
            SELECT COUNT(*) as count
            FROM attendance
            WHERE person_id = ? AND status = 'late'
            AND date BETWEEN ? AND ?
        """, (person_id, month_start, month_end))
        late_count = c.fetchone()['count']

        # 早退次数
        c.execute("""
            SELECT COUNT(*) as count
            FROM attendance
            WHERE person_id = ? AND status = 'early_leave'
            AND date BETWEEN ? AND ?
        """, (person_id, month_start, month_end))
        early_leave_count = c.fetchone()['count']

        attendance_stats = {
            'normal_days': normal_days,
            'late_count': late_count,
            'early_leave_count': early_leave_count,
            'month_start': month_start,
            'month_end': month_end
        }

    conn.close()

    return templates.TemplateResponse("user/index.html", {
        "request": request,
        "current_tab": "dashboard",
        "user": user,
        "person": person,
        "recent_records": recent_records,
        "recent_announcements": recent_announcements,
        "attendance_stats": attendance_stats,
        "logged_in_user": username
    })

# 用户公告页面
@router.get("/announcements")
async def user_announcements(request: Request, username: str = Depends(get_current_user)):
    """
    用户公告页面
    """
    # 不再需要检查用户角色，因为 get_current_user 函数已经做了检查
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取所有有效的公告 - 修改查询以确保正确获取公告
    c.execute("""
        SELECT a.id, a.title, a.content, a.type, a.priority, a.status,
               a.start_date, a.end_date, a.created_at, u.username as creator_name
        FROM announcements a
        LEFT JOIN users u ON a.created_by = u.id
        WHERE a.status = 'active'
        ORDER BY a.priority DESC, a.created_at DESC
    """)
    announcements = c.fetchall()

    conn.close()

    return templates.TemplateResponse("user/announcements.html", {
        "request": request,
        "current_tab": "announcements",
        "announcements": announcements,
        "logged_in_user": username
    })

# 用户考勤记录页面
@router.get("/attendance")
async def user_attendance(
    request: Request,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    username: str = Depends(get_current_user)
):
    """
    用户考勤记录页面
    """
    # 不再需要检查用户角色，因为 get_current_user 函数已经做了检查
    # 默认显示最近30天
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")

    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取用户ID
    c.execute("SELECT id FROM users WHERE username = ?", (username,))
    user_result = c.fetchone()
    user_id = user_result[0] if user_result else None

    # 获取用户关联的人员信息
    if user_id:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            JOIN user_person_relations upr ON p.id = upr.person_id
            WHERE upr.user_id = ?
        """, (user_id,))
        person = c.fetchone()
    else:
        person = None

    # 如果找不到关联的人员信息，尝试通过用户名查找
    if not person:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE p.name = ? OR p.employee_id = ?
        """, (username, username))
        person = c.fetchone()

    # 如果还是找不到，尝试模糊匹配
    if not person:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, d.name as department_name
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE p.name LIKE ? OR p.employee_id LIKE ?
        """, (f"%{username}%", f"%{username}%"))
        person = c.fetchone()

    records = []
    attendance_stats = {}

    if person:
        person_id = person['id']

        # 获取考勤记录
        c.execute("""
            SELECT a.id, a.timestamp, a.check_type, a.status, a.note, a.date
            FROM attendance a
            WHERE a.person_id = ?
            AND a.date BETWEEN ? AND ?
            ORDER BY a.timestamp DESC
        """, (person_id, start_date, end_date))
        records = c.fetchall()

        # 获取考勤统计
        # 正常出勤天数
        c.execute("""
            SELECT COUNT(DISTINCT date) as count
            FROM attendance
            WHERE person_id = ? AND status = 'normal'
            AND date BETWEEN ? AND ?
        """, (person_id, start_date, end_date))
        normal_days = c.fetchone()['count']

        # 迟到次数
        c.execute("""
            SELECT COUNT(*) as count
            FROM attendance
            WHERE person_id = ? AND status = 'late'
            AND date BETWEEN ? AND ?
        """, (person_id, start_date, end_date))
        late_count = c.fetchone()['count']

        # 早退次数
        c.execute("""
            SELECT COUNT(*) as count
            FROM attendance
            WHERE person_id = ? AND status = 'early_leave'
            AND date BETWEEN ? AND ?
        """, (person_id, start_date, end_date))
        early_leave_count = c.fetchone()['count']

        attendance_stats = {
            'normal_days': normal_days,
            'late_count': late_count,
            'early_leave_count': early_leave_count
        }

    conn.close()

    return templates.TemplateResponse("user/attendance.html", {
        "request": request,
        "current_tab": "attendance",
        "person": person,
        "records": records,
        "attendance_stats": attendance_stats,
        "start_date": start_date,
        "end_date": end_date,
        "logged_in_user": username
    })

# 用户个人资料页面
@router.get("/profile")
async def user_profile(request: Request, username: str = Depends(get_current_user)):
    """
    用户个人资料页面
    """
    # 不再需要检查用户角色，因为 get_current_user 函数已经做了检查
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取用户信息
    c.execute("SELECT id, username, role, email FROM users WHERE username=?", (username,))
    user = c.fetchone()

    # 获取用户ID
    c.execute("SELECT id FROM users WHERE username = ?", (username,))
    user_result = c.fetchone()
    user_id = user_result[0] if user_result else None

    # 获取用户关联的人员信息
    if user_id:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, p.face_image_path,
                   d.name as department_name, d.id as department_id
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            JOIN user_person_relations upr ON p.id = upr.person_id
            WHERE upr.user_id = ?
        """, (user_id,))
        person = c.fetchone()
    else:
        person = None

    # 如果找不到关联的人员信息，尝试通过用户名查找
    if not person:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, p.face_image_path,
                   d.name as department_name, d.id as department_id
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE p.name = ? OR p.employee_id = ?
        """, (username, username))
        person = c.fetchone()

    # 如果还是找不到，尝试模糊匹配
    if not person:
        c.execute("""
            SELECT p.id, p.name, p.gender, p.position, p.employee_id, p.face_image_path,
                   d.name as department_name, d.id as department_id
            FROM persons p
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE p.name LIKE ? OR p.employee_id LIKE ?
        """, (f"%{username}%", f"%{username}%"))
        person = c.fetchone()

    # 获取考勤规则信息
    attendance_rule = None
    if person:
        c.execute("""
            SELECT ar.id, ar.name, ar.check_in_time, ar.check_out_time,
                   ar.late_threshold, ar.early_leave_threshold, ar.work_days
            FROM attendance_rules ar
            JOIN person_attendance_rules par ON ar.id = par.rule_id
            WHERE par.person_id = ?
            AND (par.expiry_date IS NULL OR date(par.expiry_date) >= date('now', 'localtime'))
            ORDER BY par.effective_date DESC
            LIMIT 1
        """, (person['id'],))
        attendance_rule = c.fetchone()

        # 如果没有找到个人规则，使用默认规则
        if not attendance_rule:
            c.execute("""
                SELECT id, name, check_in_time, check_out_time,
                       late_threshold, early_leave_threshold, work_days
                FROM attendance_rules
                WHERE is_default = 1
                LIMIT 1
            """)
            attendance_rule = c.fetchone()

    conn.close()

    return templates.TemplateResponse("user/profile.html", {
        "request": request,
        "current_tab": "profile",
        "user": user,
        "person": person,
        "attendance_rule": attendance_rule,
        "logged_in_user": username
    })

{% extends "user/base.html" %}

{% block title %}考勤系统 - 用户仪表盘{% endblock %}

{% block page_title %}仪表盘{% endblock %}

{% block content %}
<div class="row">
    <!-- 用户信息卡片 -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-person-circle me-2"></i>
                个人信息
            </div>
            <div class="card-body">
                {% if person %}
                <div class="text-center mb-3">
                    {% if person.face_image_path %}
                    <img src="/{{ person.face_image_path }}" alt="{{ person.name }}" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                    {% else %}
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto" style="width: 100px; height: 100px;">
                        <i class="bi bi-person" style="font-size: 3rem;"></i>
                    </div>
                    {% endif %}
                </div>
                <h5 class="card-title text-center mb-3">{{ person.name }}</h5>
                <div class="mb-2">
                    <small class="text-muted">工号:</small>
                    <span class="ms-2">{{ person.employee_id if person.employee_id else '未设置' }}</span>
                </div>
                <div class="mb-2">
                    <small class="text-muted">部门:</small>
                    <span class="ms-2">{{ person.department_name if person.department_name else '未分配' }}</span>
                </div>
                <div class="mb-2">
                    <small class="text-muted">职位:</small>
                    <span class="ms-2">{{ person.position if person.position else '未设置' }}</span>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-exclamation-circle text-warning" style="font-size: 3rem;"></i>
                    <p class="mt-3">未找到关联的人员信息</p>
                    <p class="text-muted small">请联系管理员关联您的用户账号与人员信息</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 考勤统计卡片 -->
    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-bar-chart-line me-2"></i>
                本月考勤统计
                <span class="ms-auto text-muted small">
                    {{ attendance_stats.month_start }} 至 {{ attendance_stats.month_end }}
                </span>
            </div>
            <div class="card-body">
                {% if person and attendance_stats %}
                <div class="row text-center">
                    <div class="col-md-4 mb-3">
                        <div class="p-3 rounded bg-success-light">
                            <h3 class="mb-0">{{ attendance_stats.normal_days }}</h3>
                            <p class="mb-0">正常出勤天数</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="p-3 rounded bg-warning-light">
                            <h3 class="mb-0">{{ attendance_stats.late_count }}</h3>
                            <p class="mb-0">迟到次数</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="p-3 rounded bg-danger-light">
                            <h3 class="mb-0">{{ attendance_stats.early_leave_count }}</h3>
                            <p class="mb-0">早退次数</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3" style="max-width: 300px; margin: 0 auto;">
                    <canvas id="attendanceChart" height="150"></canvas>
                </div>
                {% elif person %}
                <div class="text-center py-4">
                    <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3">暂无考勤数据</p>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <p class="text-muted">请先关联人员信息</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近考勤记录 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-clock-history me-2"></i>
                最近考勤记录
            </div>
            <div class="card-body">
                {% if recent_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>时间</th>
                                <th>类型</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>{{ record.timestamp.split(' ')[0] }}</td>
                                <td>{{ record.timestamp.split(' ')[1] }}</td>
                                <td>
                                    {% if record.check_type == 'check_in' %}
                                    <span class="badge bg-primary">上班打卡</span>
                                    {% elif record.check_type == 'check_out' %}
                                    <span class="badge bg-success">下班打卡</span>
                                    {% else %}
                                    <span class="badge bg-secondary">未知</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.status == 'normal' %}
                                    <span class="badge bg-success">正常</span>
                                    {% elif record.status == 'late' %}
                                    <span class="badge bg-warning">迟到</span>
                                    {% elif record.status == 'early_leave' %}
                                    <span class="badge bg-warning">早退</span>
                                    {% elif record.status == 'absent' %}
                                    <span class="badge bg-danger">缺勤</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ record.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-end mt-2">
                    <a href="/user/attendance" class="btn btn-sm btn-outline-primary">查看全部记录</a>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3">暂无考勤记录</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 最新公告 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-megaphone me-2"></i>
                最新公告
            </div>
            <div class="card-body">
                {% if recent_announcements %}
                <div class="list-group">
                    {% for announcement in recent_announcements %}
                    <div class="list-group-item list-group-item-action flex-column align-items-start">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">
                                {% if announcement.type == 'important' %}
                                <span class="badge bg-warning me-1">重要</span>
                                {% elif announcement.type == 'urgent' %}
                                <span class="badge bg-danger me-1">紧急</span>
                                {% endif %}
                                {{ announcement.title }}
                            </h5>
                            <small>{{ announcement.created_at }}</small>
                        </div>
                        <p class="mb-1">{{ announcement.content|truncate(100) }}</p>
                        <small>
                            <a href="#" data-bs-toggle="modal" data-bs-target="#announcementModal"
                               data-title="{{ announcement.title }}"
                               data-content="{{ announcement.content }}">
                                查看详情
                            </a>
                        </small>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-end mt-3">
                    <a href="/user/announcements" class="btn btn-sm btn-outline-primary">查看全部公告</a>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-bell-slash text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3">暂无公告</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 公告详情模态框 -->
<div class="modal fade" id="announcementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="announcementModalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="announcementModalBody">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 公告详情模态框
        $('#announcementModal').on('show.bs.modal', function (event) {
            const button = $(event.relatedTarget);
            const title = button.data('title');
            const content = button.data('content');

            $('#announcementModalTitle').text(title);
            $('#announcementModalBody').html(content.replace(/\n/g, '<br>'));
        });

        // 考勤统计图表
        {% if person and attendance_stats %}
        const ctx = document.getElementById('attendanceChart').getContext('2d');
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['正常出勤', '迟到', '早退'],
                datasets: [{
                    data: [
                        {{ attendance_stats.normal_days }},
                        {{ attendance_stats.late_count }},
                        {{ attendance_stats.early_leave_count }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        {% endif %}
    });
</script>
{% endblock %}

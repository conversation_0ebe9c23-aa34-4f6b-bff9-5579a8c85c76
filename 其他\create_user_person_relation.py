import sqlite3

def create_relation(user_id, person_id):
    """创建用户与人员的关联关系"""
    conn = sqlite3.connect('face_recognition.db')
    c = conn.cursor()
    
    try:
        # 检查用户是否存在
        c.execute("SELECT id, username FROM users WHERE id=?", (user_id,))
        user = c.fetchone()
        if not user:
            print(f"错误: 用户ID {user_id} 不存在")
            return False
            
        # 检查人员是否存在
        c.execute("SELECT id, name FROM persons WHERE id=?", (person_id,))
        person = c.fetchone()
        if not person:
            print(f"错误: 人员ID {person_id} 不存在")
            return False
            
        # 检查关联是否已存在
        c.execute("SELECT id FROM user_person_relations WHERE user_id=? AND person_id=?", 
                 (user_id, person_id))
        if c.fetchone():
            print(f"关联已存在: 用户ID {user_id} 已经与人员ID {person_id} 关联")
            return False
            
        # 创建关联
        c.execute("INSERT INTO user_person_relations (user_id, person_id) VALUES (?, ?)",
                 (user_id, person_id))
        conn.commit()
        print(f"成功创建关联: 用户ID {user_id} (用户名: {user[1]}) 与人员ID {person_id} (姓名: {person[1]})")
        return True
        
    except Exception as e:
        print(f"创建关联时出错: {str(e)}")
        return False
    finally:
        conn.close()

def main():
    # 获取用户列表
    conn = sqlite3.connect('face_recognition.db')
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    print("=== 可用用户 ===")
    c.execute("SELECT id, username, role FROM users")
    users = c.fetchall()
    for user in users:
        print(f"ID: {user['id']}, 用户名: {user['username']}, 角色: {user['role']}")
    
    print("\n=== 可用人员 ===")
    c.execute("SELECT id, name, employee_id FROM persons")
    persons = c.fetchall()
    for person in persons:
        print(f"ID: {person['id']}, 姓名: {person['name']}, 工号: {person['employee_id'] or '无'}")
    
    conn.close()
    
    # 创建关联
    user_id = int(input("\n请输入要关联的用户ID: "))
    person_id = int(input("请输入要关联的人员ID: "))
    
    create_relation(user_id, person_id)
    
    # 显示当前关联
    conn = sqlite3.connect('face_recognition.db')
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    print("\n=== 当前关联 ===")
    c.execute("""
        SELECT r.id, u.username, p.name, p.employee_id
        FROM user_person_relations r
        JOIN users u ON r.user_id = u.id
        JOIN persons p ON r.person_id = p.id
    """)
    relations = c.fetchall()
    if relations:
        for relation in relations:
            print(f"ID: {relation['id']}, 用户名: {relation['username']}, 人员姓名: {relation['name']}, 工号: {relation['employee_id'] or '无'}")
    else:
        print("当前没有关联")
    
    conn.close()

if __name__ == "__main__":
    # 直接创建关联 user1 (ID: 2) 与 刘昀达 (ID: 1)
    create_relation(2, 1)
    
    # 显示当前关联
    conn = sqlite3.connect('face_recognition.db')
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    print("\n=== 当前关联 ===")
    c.execute("""
        SELECT r.id, u.username, p.name, p.employee_id
        FROM user_person_relations r
        JOIN users u ON r.user_id = u.id
        JOIN persons p ON r.person_id = p.id
    """)
    relations = c.fetchall()
    if relations:
        for relation in relations:
            print(f"ID: {relation['id']}, 用户名: {relation['username']}, 人员姓名: {relation['name']}, 工号: {relation['employee_id'] or '无'}")
    else:
        print("当前没有关联")
    
    conn.close()

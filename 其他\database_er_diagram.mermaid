erDiagram
    USERS ||--o{ USER_PERSON_RELATIONS : "关联"
    USERS {
        int id PK
        string username UK
        string password_hash
        string role
        string email
        datetime created_at
        datetime last_login
    }
    
    PERSONS ||--o{ USER_PERSON_RELATIONS : "关联"
    PERSONS ||--o{ ATTENDANCE : "记录"
    PERSONS ||--o{ LEAVE_REQUESTS : "申请"
    PERSONS }|--|| DEPARTMENTS : "属于"
    PERSONS {
        int id PK
        string name
        string gender
        string position
        int department_id FK
        string employee_id UK
        string face_image_path UK
        string status
        datetime created_at
    }
    
    USER_PERSON_RELATIONS {
        int id PK
        int user_id FK
        int person_id FK
        datetime created_at
    }
    
    DEPARTMENTS ||--o{ PERSONS : "包含"
    DEPARTMENTS ||--o{ DEPARTMENT_RULES : "应用"
    DEPARTMENTS }o--|| DEPARTMENTS : "上级部门"
    DEPARTMENTS {
        int id PK
        string name UK
        int parent_id FK
        int manager_id FK
        string description
        datetime created_at
    }
    
    ATTENDANCE {
        int id PK
        int person_id FK
        datetime timestamp
        string type
        string status
        string note
        int created_by FK
    }
    
    SHIFTS {
        int id PK
        string name
        string start_time
        string end_time
        int late_threshold
        int early_leave_threshold
        string description
        datetime created_at
    }
    
    ATTENDANCE_RULES ||--o{ DEPARTMENT_RULES : "应用于"
    ATTENDANCE_RULES ||--o{ PERSON_RULES : "应用于"
    ATTENDANCE_RULES {
        int id PK
        string name
        string description
        string check_in_time
        string check_out_time
        int late_threshold
        int early_leave_threshold
        string work_days
        boolean location_required
        int location_range
        float latitude
        float longitude
        boolean is_default
        datetime created_at
    }
    
    DEPARTMENT_RULES {
        int id PK
        int department_id FK
        int rule_id FK
        date effective_from
        date effective_to
    }
    
    PERSON_RULES {
        int id PK
        int person_id FK
        int rule_id FK
        date effective_from
        date effective_to
    }
    
    LEAVE_TYPES ||--o{ LEAVE_REQUESTS : "类型"
    LEAVE_TYPES {
        int id PK
        string name UK
        string description
        boolean paid
        string color
        datetime created_at
    }
    
    LEAVE_REQUESTS {
        int id PK
        int person_id FK
        int leave_type_id FK
        date start_date
        date end_date
        int start_half
        int end_half
        string reason
        string status
        int approver_id FK
        datetime approved_at
        datetime created_at
    }
    
    NOTIFICATIONS {
        int id PK
        int user_id FK
        string title
        string message
        string type
        string related_to
        int related_id
        boolean is_read
        datetime created_at
    }
    
    SETTINGS {
        int id PK
        string key UK
        string value
        string description
        datetime updated_at
    }
    
    USERS ||--o{ NOTIFICATIONS : "接收"
    USERS ||--o{ USER_ROLES : "拥有"
    
    ROLES ||--o{ USER_ROLES : "分配"
    ROLES {
        int id PK
        string name UK
        string description
        string permissions
        datetime created_at
    }
    
    USER_ROLES {
        int id PK
        int user_id FK
        int role_id FK
    }

#!/usr/bin/env python3
import sqlite3
import hashlib
import io
import json
from datetime import datetime, timedelta
from typing import Optional, List

from fastapi import APIRouter, Request, Depends, HTTPException, Form, Query, File, UploadFile
from fastapi.templating import Jinja2Templates
from fastapi.responses import RedirectResponse, StreamingResponse, JSONResponse

try:
    import xlsxwriter
except ImportError:
    # 如果没有安装xlsxwriter，使用其他方式导出Excel
    xlsxwriter = None

from .db_init import DATABASE

router = APIRouter(
    prefix="/admin",
    tags=["admin"],
    responses={404: {"description": "Not found"}},
)

templates = Jinja2Templates(directory="templates")

# 管理员身份验证 - 使用会话
async def get_current_admin_user(request: Request):
    """
    验证用户身份并确保是管理员
    """
    username = request.session.get("username")
    role = request.session.get("role")

    if not username:
        raise HTTPException(
            status_code=303,  # See Other
            detail="未登录",
            headers={"Location": "/login"}
        )

    if role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=303,  # See Other
            detail="无权访问管理员页面",
            headers={"Location": "/user/"}
        )

    return username

@router.get("/")
async def admin_dashboard(request: Request, username: str = Depends(get_current_admin_user)):
    """
    管理后台首页
    """
    # 获取基本统计信息
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 用户数量
    c.execute("SELECT COUNT(*) as count FROM users")
    user_count = c.fetchone()['count']

    # 人员数量
    c.execute("SELECT COUNT(*) as count FROM persons")
    person_count = c.fetchone()['count']

    # 部门数量
    c.execute("SELECT COUNT(*) as count FROM departments")
    dept_count = c.fetchone()['count']

    # 今日考勤记录
    today = datetime.now().strftime("%Y-%m-%d")
    c.execute("SELECT COUNT(*) as count FROM attendance WHERE date(timestamp) = ?", (today,))
    today_attendance = c.fetchone()['count']

    # 最近的考勤记录
    c.execute("""
        SELECT a.timestamp, p.name, p.position, a.status
        FROM attendance a
        JOIN persons p ON a.person_id = p.id
        ORDER BY a.timestamp DESC LIMIT 5
    """)
    recent_records = c.fetchall()

    # 最新公告
    c.execute("""
        SELECT a.*, u.username as creator_name
        FROM announcements a
        LEFT JOIN users u ON a.created_by = u.id
        WHERE (a.start_date IS NULL OR date(a.start_date) <= date('now', 'localtime'))
        AND (a.end_date IS NULL OR date(a.end_date) >= date('now', 'localtime'))
        AND a.status = 'active'
        ORDER BY a.priority DESC, a.created_at DESC
        LIMIT 3
    """)
    recent_announcements = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/index.html", {
        "request": request,
        "current_tab": "dashboard",
        "user_count": user_count,
        "person_count": person_count,
        "dept_count": dept_count,
        "today_attendance": today_attendance,
        "recent_records": recent_records,
        "recent_announcements": recent_announcements,
        "logged_in_user": username
    })

# 用户管理路由
@router.get("/users")
async def manage_users(request: Request, username: str = Depends(get_current_admin_user)):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取用户列表
    c.execute("""
        SELECT u.id, u.username, u.role, u.email, u.created_at, u.last_login
        FROM users u
        ORDER BY u.id
    """)
    users = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/users.html", {
        "request": request,
        "current_tab": "users",
        "users": users,
        "logged_in_user": username
    })

@router.get("/users/new")
async def new_user_form(request: Request, username: str = Depends(get_current_admin_user)):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取角色列表
    c.execute("SELECT id, name FROM roles")
    roles = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/user_form.html", {
        "request": request,
        "current_tab": "users",
        "user": None,
        "roles": roles,
        "logged_in_user": username
    })

@router.post("/users")
async def create_user(
    request: Request,
    username: str = Depends(get_current_admin_user),
    form_username: str = Form(...),
    password: str = Form(...),
    email: str = Form(None),
    role: str = Form("user")
):
    from .db_init import get_password_hash

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查用户名是否已存在
        c.execute("SELECT id FROM users WHERE username = ?", (form_username,))
        if c.fetchone():
            conn.rollback()

            # 获取角色列表
            c.execute("SELECT id, name FROM roles")
            roles = c.fetchall()

            return templates.TemplateResponse("admin/user_form.html", {
                "request": request,
                "current_tab": "users",
                "user": None,
                "roles": roles,
                "error": "用户名已存在",
                "logged_in_user": username
            })

        # 创建用户
        password_hash = get_password_hash(password)
        c.execute("""
            INSERT INTO users (username, password_hash, email, role, created_at)
            VALUES (?, ?, ?, ?, datetime('now', 'localtime'))
        """, (form_username, password_hash, email, role))

        conn.commit()
        return RedirectResponse(url="/admin/users", status_code=303)
    except Exception as e:
        conn.rollback()
        return templates.TemplateResponse("admin/user_form.html", {
            "request": request,
            "current_tab": "users",
            "user": None,
            "error": f"创建用户失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/users/{user_id}/edit")
async def edit_user_form(
    request: Request,
    user_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取用户信息
    c.execute("SELECT id, username, email, role FROM users WHERE id=?", (user_id,))
    user = c.fetchone()

    if not user:
        conn.close()
        raise HTTPException(status_code=404, detail="用户不存在")

    # 获取角色列表
    c.execute("SELECT id, name FROM roles")
    roles = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/user_form.html", {
        "request": request,
        "current_tab": "users",
        "user": user,
        "roles": roles,
        "logged_in_user": username
    })

@router.post("/users/{user_id}")
async def update_user(
    request: Request,
    user_id: int,
    username: str = Depends(get_current_admin_user),
    form_username: str = Form(...),
    password: str = Form(None),
    email: str = Form(None),
    role: str = Form("user")
):
    from .db_init import get_password_hash

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查用户名是否已被其他用户使用
        c.execute("SELECT id FROM users WHERE username = ? AND id != ?", (form_username, user_id))
        if c.fetchone():
            conn.rollback()

            # 获取角色列表
            c.execute("SELECT id, name FROM roles")
            roles = c.fetchall()

            return templates.TemplateResponse("admin/user_form.html", {
                "request": request,
                "current_tab": "users",
                "user": {
                    "id": user_id,
                    "username": form_username,
                    "email": email,
                    "role": role
                },
                "roles": roles,
                "error": "用户名已被使用",
                "logged_in_user": username
            })

        # 更新用户
        if password:
            password_hash = get_password_hash(password)
            c.execute("""
                UPDATE users
                SET username=?, password_hash=?, email=?, role=?
                WHERE id=?
            """, (form_username, password_hash, email, role, user_id))
        else:
            c.execute("""
                UPDATE users
                SET username=?, email=?, role=?
                WHERE id=?
            """, (form_username, email, role, user_id))

        conn.commit()
        return RedirectResponse(url="/admin/users", status_code=303)
    except Exception as e:
        conn.rollback()
        return templates.TemplateResponse("admin/user_form.html", {
            "request": request,
            "current_tab": "users",
            "user": {
                "id": user_id,
                "username": form_username,
                "email": email,
                "role": role
            },
            "error": f"更新用户失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/users/{user_id}/delete")
async def delete_user(
    user_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查是否为最后一个管理员
        c.execute("SELECT COUNT(*) FROM users WHERE role='admin'")
        admin_count = c.fetchone()[0]

        c.execute("SELECT role FROM users WHERE id=?", (user_id,))
        user_role = c.fetchone()[0]

        if user_role == 'admin' and admin_count <= 1:
            conn.close()
            raise HTTPException(
                status_code=400,
                detail="不能删除最后一个管理员账号"
            )

        # 删除用户
        c.execute("DELETE FROM users WHERE id=?", (user_id,))
        conn.commit()

        return RedirectResponse(url="/admin/users", status_code=303)
    except HTTPException as e:
        raise e
    except Exception as e:
        conn.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除用户失败: {str(e)}"
        )
    finally:
        conn.close()

# 部门管理路由
@router.get("/departments")
async def manage_departments(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取部门列表
    c.execute("""
        SELECT d.id, d.name, d.description,
               p.name as parent_name, p.id as parent_id,
               m.name as manager_name, m.id as manager_id
        FROM departments d
        LEFT JOIN departments p ON d.parent_id = p.id
        LEFT JOIN persons m ON d.manager_id = m.id
        ORDER BY d.id
    """)
    departments = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/departments.html", {
        "request": request,
        "current_tab": "departments",
        "departments": departments,
        "logged_in_user": username
    })

@router.get("/departments/new")
async def new_department_form(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取部门列表（用于选择父部门）
    c.execute("SELECT id, name FROM departments ORDER BY name")
    all_departments = c.fetchall()

    # 获取人员列表（用于选择部门经理）
    c.execute("SELECT id, name FROM persons ORDER BY name")
    persons = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/department_form.html", {
        "request": request,
        "current_tab": "departments",
        "department": None,
        "all_departments": all_departments,
        "persons": persons,
        "logged_in_user": username
    })

@router.post("/departments")
async def create_department(
    request: Request,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    description: str = Form(None),
    parent_id: str = Form(None),
    manager_id: str = Form(None),
    status: int = Form(1)
):
    # 处理parent_id和manager_id
    if parent_id == "":
        parent_id = None
    elif parent_id is not None:
        try:
            parent_id = int(parent_id)
        except ValueError:
            parent_id = None

    if manager_id == "":
        manager_id = None
    elif manager_id is not None:
        try:
            manager_id = int(manager_id)
        except ValueError:
            manager_id = None

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查部门名是否已存在
        c.execute("SELECT id FROM departments WHERE name = ?", (name,))
        if c.fetchone():
            conn.rollback()

            # 获取部门列表和人员列表
            c.execute("SELECT id, name FROM departments ORDER BY name")
            all_departments = c.fetchall()

            c.execute("SELECT id, name FROM persons ORDER BY name")
            persons = c.fetchall()

            return templates.TemplateResponse("admin/department_form.html", {
                "request": request,
                "current_tab": "departments",
                "department": {
                    "name": name,
                    "description": description,
                    "parent_id": parent_id,
                    "manager_id": manager_id
                },
                "all_departments": all_departments,
                "persons": persons,
                "error": "部门名称已存在",
                "logged_in_user": username
            })

        # 创建部门
        c.execute("""
            INSERT INTO departments (name, description, parent_id, manager_id, status)
            VALUES (?, ?, ?, ?, ?)
        """, (name, description, parent_id, manager_id, status))

        conn.commit()
        return RedirectResponse(url="/admin/departments", status_code=303)
    except Exception as e:
        conn.rollback()

        # 获取部门列表和人员列表
        c.execute("SELECT id, name FROM departments ORDER BY name")
        all_departments = c.fetchall()

        c.execute("SELECT id, name FROM persons ORDER BY name")
        persons = c.fetchall()

        return templates.TemplateResponse("admin/department_form.html", {
            "request": request,
            "current_tab": "departments",
            "department": {
                "name": name,
                "description": description,
                "parent_id": parent_id,
                "manager_id": manager_id
            },
            "all_departments": all_departments,
            "persons": persons,
            "error": f"创建部门失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/departments/{department_id}/edit")
async def edit_department_form(
    request: Request,
    department_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取部门信息
    c.execute("""
        SELECT id, name, description, parent_id, manager_id
        FROM departments WHERE id=?
    """, (department_id,))
    department = c.fetchone()

    if not department:
        conn.close()
        raise HTTPException(status_code=404, detail="部门不存在")

    # 获取部门列表（用于选择父部门）
    c.execute("SELECT id, name FROM departments WHERE id != ? ORDER BY name", (department_id,))
    all_departments = c.fetchall()

    # 获取人员列表（用于选择部门经理）
    c.execute("SELECT id, name FROM persons ORDER BY name")
    persons = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/department_form.html", {
        "request": request,
        "current_tab": "departments",
        "department": department,
        "all_departments": all_departments,
        "persons": persons,
        "logged_in_user": username
    })

@router.post("/departments/{department_id}")
async def update_department(
    request: Request,
    department_id: int,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    description: str = Form(None),
    parent_id: str = Form(None),
    manager_id: str = Form(None),
    status: int = Form(1)
):
    # 处理parent_id和manager_id
    if parent_id == "":
        parent_id = None
    elif parent_id is not None:
        try:
            parent_id = int(parent_id)
            # 防止循环引用
            if parent_id == department_id:
                parent_id = None
        except ValueError:
            parent_id = None

    if manager_id == "":
        manager_id = None
    elif manager_id is not None:
        try:
            manager_id = int(manager_id)
        except ValueError:
            manager_id = None

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查部门名是否已被其他部门使用
        c.execute("SELECT id FROM departments WHERE name = ? AND id != ?", (name, department_id))
        if c.fetchone():
            conn.rollback()

            # 获取部门列表和人员列表
            c.execute("SELECT id, name FROM departments WHERE id != ? ORDER BY name", (department_id,))
            all_departments = c.fetchall()

            c.execute("SELECT id, name FROM persons ORDER BY name")
            persons = c.fetchall()

            return templates.TemplateResponse("admin/department_form.html", {
                "request": request,
                "current_tab": "departments",
                "department": {
                    "id": department_id,
                    "name": name,
                    "description": description,
                    "parent_id": parent_id,
                    "manager_id": manager_id
                },
                "all_departments": all_departments,
                "persons": persons,
                "error": "部门名称已存在",
                "logged_in_user": username
            })

        # 更新部门
        c.execute("""
            UPDATE departments
            SET name=?, description=?, parent_id=?, manager_id=?, status=?
            WHERE id=?
        """, (name, description, parent_id, manager_id, status, department_id))

        conn.commit()
        return RedirectResponse(url="/admin/departments", status_code=303)
    except Exception as e:
        conn.rollback()

        # 获取部门列表和人员列表
        c.execute("SELECT id, name FROM departments WHERE id != ? ORDER BY name", (department_id,))
        all_departments = c.fetchall()

        c.execute("SELECT id, name FROM persons ORDER BY name")
        persons = c.fetchall()

        return templates.TemplateResponse("admin/department_form.html", {
            "request": request,
            "current_tab": "departments",
            "department": {
                "id": department_id,
                "name": name,
                "description": description,
                "parent_id": parent_id,
                "manager_id": manager_id
            },
            "all_departments": all_departments,
            "persons": persons,
            "error": f"更新部门失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/departments/{department_id}/delete")
async def delete_department(
    department_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查是否有子部门
        c.execute("SELECT COUNT(*) FROM departments WHERE parent_id=?", (department_id,))
        child_count = c.fetchone()[0]

        if child_count > 0:
            conn.close()
            raise HTTPException(
                status_code=400,
                detail=f"无法删除该部门，存在{child_count}个子部门"
            )

        # 检查是否有关联人员
        c.execute("SELECT COUNT(*) FROM persons WHERE department_id=?", (department_id,))
        person_count = c.fetchone()[0]

        if person_count > 0:
            conn.close()
            raise HTTPException(
                status_code=400,
                detail=f"无法删除该部门，存在{person_count}个关联人员"
            )

        # 删除部门
        c.execute("DELETE FROM departments WHERE id=?", (department_id,))
        conn.commit()

        return RedirectResponse(url="/admin/departments", status_code=303)
    except HTTPException as e:
        raise e
    except Exception as e:
        conn.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除部门失败: {str(e)}"
        )
    finally:
        conn.close()

# 人员管理路由
@router.get("/persons")
async def manage_persons(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取人员列表
    c.execute("""
        SELECT p.id, p.name, p.gender, p.position, p.employee_id, p.face_image_path,
               d.name as department_name, d.id as department_id
        FROM persons p
        LEFT JOIN departments d ON p.department_id = d.id
        ORDER BY p.id
    """)
    persons = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/persons.html", {
        "request": request,
        "current_tab": "persons",
        "persons": persons,
        "logged_in_user": username
    })

@router.get("/persons/new")
async def new_person_form(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取部门列表
    c.execute("SELECT id, name FROM departments ORDER BY name")
    departments = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/person_form.html", {
        "request": request,
        "current_tab": "persons",
        "person": None,
        "departments": departments,
        "logged_in_user": username
    })

@router.post("/persons")
async def create_person(
    request: Request,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    gender: str = Form(None),
    position: str = Form(None),
    employee_id: str = Form(None),
    department_id: str = Form(None),
    face_image: UploadFile = File(None),
    register_face: bool = Form(False)
):
    # 处理department_id
    if department_id == "":
        department_id = None
    elif department_id is not None:
        try:
            department_id = int(department_id)
        except ValueError:
            department_id = None

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 处理人脸图片
        face_image_path = None
        if face_image and face_image.filename:
            # 生成唯一文件名
            import uuid
            import os
            file_ext = os.path.splitext(face_image.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            face_image_path = f"known_faces/{unique_filename}"

            # 保存文件
            contents = await face_image.read()
            with open(face_image_path, "wb") as f:
                f.write(contents)

        # 创建人员记录
        c.execute("""
            INSERT INTO persons (name, gender, position, employee_id, department_id, face_image_path, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))
        """, (name, gender, position, employee_id, department_id, face_image_path))

        # 获取新插入的人员ID
        person_id = c.lastrowid

        conn.commit()

        # 如果选择了录入人脸，跳转到人脸录入页面
        if register_face:
            return RedirectResponse(url=f"/admin/persons/{person_id}/face", status_code=303)
        else:
            return RedirectResponse(url="/admin/persons", status_code=303)
    except Exception as e:
        conn.rollback()

        # 获取部门列表
        c.execute("SELECT id, name FROM departments ORDER BY name")
        departments = c.fetchall()

        return templates.TemplateResponse("admin/person_form.html", {
            "request": request,
            "current_tab": "persons",
            "person": {
                "name": name,
                "gender": gender,
                "position": position,
                "employee_id": employee_id,
                "department_id": department_id
            },
            "departments": departments,
            "error": f"创建人员失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/persons/{person_id}/edit")
async def edit_person_form(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取人员信息
    c.execute("""
        SELECT id, name, gender, position, employee_id, department_id, face_image_path
        FROM persons WHERE id=?
    """, (person_id,))
    person = c.fetchone()

    if not person:
        conn.close()
        raise HTTPException(status_code=404, detail="人员不存在")

    # 获取部门列表
    c.execute("SELECT id, name FROM departments ORDER BY name")
    departments = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/person_form.html", {
        "request": request,
        "current_tab": "persons",
        "person": person,
        "departments": departments,
        "logged_in_user": username
    })

@router.post("/persons/{person_id}")
async def update_person(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    gender: str = Form(None),
    position: str = Form(None),
    employee_id: str = Form(None),
    department_id: str = Form(None),
    face_image: UploadFile = File(None),
    register_face: bool = Form(False)
):
    # 处理department_id
    if department_id == "":
        department_id = None
    elif department_id is not None:
        try:
            department_id = int(department_id)
        except ValueError:
            department_id = None

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 获取原有人脸图片路径
        c.execute("SELECT face_image_path FROM persons WHERE id=?", (person_id,))
        result = c.fetchone()
        if not result:
            conn.close()
            raise HTTPException(status_code=404, detail="人员不存在")

        face_image_path = result[0]

        # 处理新上传的人脸图片
        if face_image and face_image.filename:
            # 生成唯一文件名
            import uuid
            import os
            file_ext = os.path.splitext(face_image.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            face_image_path = f"known_faces/{unique_filename}"

            # 保存文件
            contents = await face_image.read()
            with open(face_image_path, "wb") as f:
                f.write(contents)

        # 更新人员记录
        c.execute("""
            UPDATE persons
            SET name=?, gender=?, position=?, employee_id=?, department_id=?, face_image_path=?
            WHERE id=?
        """, (name, gender, position, employee_id, department_id, face_image_path, person_id))

        conn.commit()

        # 如果选择了录入人脸，跳转到人脸录入页面
        if register_face:
            return RedirectResponse(url=f"/admin/persons/{person_id}/face", status_code=303)
        else:
            return RedirectResponse(url="/admin/persons", status_code=303)
    except Exception as e:
        conn.rollback()

        # 获取部门列表
        c.execute("SELECT id, name FROM departments ORDER BY name")
        departments = c.fetchall()

        return templates.TemplateResponse("admin/person_form.html", {
            "request": request,
            "current_tab": "persons",
            "person": {
                "id": person_id,
                "name": name,
                "gender": gender,
                "position": position,
                "employee_id": employee_id,
                "department_id": department_id
            },
            "departments": departments,
            "error": f"更新人员失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/persons/{person_id}/face")
async def face_register_form(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取人员信息
    c.execute("""
        SELECT p.*, d.name as department_name
        FROM persons p
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE p.id=?
    """, (person_id,))
    person = c.fetchone()

    if not person:
        conn.close()
        raise HTTPException(status_code=404, detail="人员不存在")

    conn.close()

    return templates.TemplateResponse("admin/face_register.html", {
        "request": request,
        "current_tab": "persons",
        "person": person,
        "department_name": person["department_name"] if person["department_name"] else "未分配",
        "logged_in_user": username
    })

@router.post("/persons/{person_id}/face")
async def update_person_face(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user),
    face_image: UploadFile = File(...)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 获取原有人脸图片路径
        c.execute("SELECT face_image_path FROM persons WHERE id=?", (person_id,))
        result = c.fetchone()
        if not result:
            conn.close()
            return JSONResponse(
                status_code=404,
                content={"detail": "人员不存在"}
            )

        old_face_image_path = result[0]

        # 处理新上传的人脸图片
        if face_image and face_image.filename:
            # 生成唯一文件名
            import uuid
            import os
            file_ext = os.path.splitext(face_image.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            face_image_path = f"known_faces/{unique_filename}"

            # 保存文件
            contents = await face_image.read()
            with open(face_image_path, "wb") as f:
                f.write(contents)

            # 删除旧文件
            if old_face_image_path and os.path.exists(old_face_image_path):
                try:
                    os.remove(old_face_image_path)
                except OSError:
                    pass

            # 更新人脸图片路径
            c.execute("""
                UPDATE persons
                SET face_image_path=?
                WHERE id=?
            """, (face_image_path, person_id))

            conn.commit()
            return JSONResponse(
                status_code=200,
                content={"success": True, "message": "人脸录入成功"}
            )
        else:
            return JSONResponse(
                status_code=400,
                content={"detail": "未提供人脸图片"}
            )
    except Exception as e:
        conn.rollback()
        return JSONResponse(
            status_code=500,
            content={"detail": f"更新人脸失败: {str(e)}"}
        )
    finally:
        conn.close()

@router.post("/persons/{person_id}/delete")
async def delete_person(
    person_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查是否有考勤记录
        c.execute("SELECT COUNT(*) FROM attendance WHERE person_id=?", (person_id,))
        attendance_count = c.fetchone()[0]

        if attendance_count > 0:
            conn.close()
            raise HTTPException(
                status_code=400,
                detail=f"无法删除该人员，存在{attendance_count}条相关考勤记录"
            )

        # 删除人员
        c.execute("DELETE FROM persons WHERE id=?", (person_id,))
        conn.commit()

        return RedirectResponse(url="/admin/persons", status_code=303)
    except HTTPException as e:
        raise e
    except Exception as e:
        conn.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除人员失败: {str(e)}"
        )
    finally:
        conn.close()

# 考勤数据路由
@router.get("/attendance")
async def manage_attendance(
    request: Request,
    username: str = Depends(get_current_admin_user),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    department_id: Optional[str] = None
):
    # 处理department_id为空字符串的情况
    if department_id == "":
        department_id = None
    elif department_id is not None:
        try:
            department_id = int(department_id)
        except ValueError:
            department_id = None
    # 默认显示最近7天
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")

    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 构建查询
    query = """
        SELECT a.id, p.id as person_id, p.name, p.employee_id, d.name as department,
               a.timestamp, a.type, a.status, a.note, a.check_type
        FROM attendance a
        JOIN persons p ON a.person_id = p.id
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE date(a.timestamp) BETWEEN ? AND ?
    """
    params = [start_date, end_date]

    if department_id:
        query += " AND p.department_id = ?"
        params.append(department_id)

    query += " ORDER BY a.timestamp DESC"

    c.execute(query, params)
    records = c.fetchall()

    # 获取部门列表
    c.execute("SELECT id, name FROM departments ORDER BY name")
    departments = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/attendance.html", {
        "request": request,
        "current_tab": "attendance",
        "records": records,
        "departments": departments,
        "start_date": start_date,
        "end_date": end_date,
        "selected_department": department_id,
        "logged_in_user": username
    })

# 考勤记录编辑路由
@router.get("/attendance/{attendance_id}/edit")
async def edit_attendance_form(
    request: Request,
    attendance_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取考勤记录信息
    c.execute("""
        SELECT a.*, p.name as person_name, p.employee_id, d.name as department_name
        FROM attendance a
        JOIN persons p ON a.person_id = p.id
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE a.id = ?
    """, (attendance_id,))
    attendance = c.fetchone()

    if not attendance:
        conn.close()
        raise HTTPException(status_code=404, detail="考勤记录不存在")

    # 获取人员列表
    c.execute("SELECT id, name FROM persons ORDER BY name")
    persons = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/attendance_form.html", {
        "request": request,
        "current_tab": "attendance",
        "attendance": attendance,
        "persons": persons,
        "logged_in_user": username
    })

@router.post("/attendance/{attendance_id}")
async def update_attendance(
    request: Request,
    attendance_id: int,
    username: str = Depends(get_current_admin_user),
    person_id: int = Form(...),
    date: str = Form(...),
    check_in: Optional[str] = Form(None),
    check_out: Optional[str] = Form(None),
    type: str = Form("check_in"),
    status: str = Form("normal"),
    note: Optional[str] = Form(None)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 根据打卡类型决定使用哪个时间
        time_value = check_in if type == "check_in" else check_out

        # 如果没有提供时间，使用当前时间
        if not time_value:
            time_value = datetime.now().strftime("%H:%M")

        # 组合日期和时间
        timestamp = f"{date} {time_value}"

        # 更新考勤记录
        c.execute("""
            UPDATE attendance
            SET person_id=?, timestamp=?, type=?, status=?, note=?, check_type=?
            WHERE id=?
        """, (person_id, timestamp, type, status, note, type, attendance_id))

        conn.commit()
        return RedirectResponse(url="/admin/attendance", status_code=303)
    except Exception as e:
        conn.rollback()

        # 获取人员列表
        c.execute("SELECT id, name FROM persons ORDER BY name")
        persons = c.fetchall()

        # 获取原考勤记录信息
        c.execute("""
            SELECT a.*, p.name as person_name, p.employee_id, d.name as department_name
            FROM attendance a
            JOIN persons p ON a.person_id = p.id
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE a.id = ?
        """, (attendance_id,))
        attendance = c.fetchone()

        if not attendance:
            # 组合日期和时间
            time_value = check_in if type == "check_in" else check_out
            if not time_value:
                time_value = datetime.now().strftime("%H:%M")
            timestamp_str = f"{date} {time_value}"

            attendance = {
                "id": attendance_id,
                "person_id": person_id,
                "timestamp": timestamp_str,
                "type": type,
                "check_type": type,
                "status": status,
                "note": note
            }

        return templates.TemplateResponse("admin/attendance_form.html", {
            "request": request,
            "current_tab": "attendance",
            "attendance": attendance,
            "persons": persons,
            "error": f"更新考勤记录失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/attendance/{attendance_id}/delete")
async def delete_attendance(
    attendance_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 删除考勤记录
        c.execute("DELETE FROM attendance WHERE id=?", (attendance_id,))
        conn.commit()

        return RedirectResponse(url="/admin/attendance", status_code=303)
    except Exception as e:
        conn.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除考勤记录失败: {str(e)}"
        )
    finally:
        conn.close()

# 考勤规则路由
@router.get("/attendance-rules")
async def manage_attendance_rules(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取考勤规则
    c.execute("SELECT id, name, description, check_in_time, check_out_time, work_days, location_required, is_default FROM attendance_rules ORDER BY id")
    rules = c.fetchall()

    # 获取班次
    c.execute("SELECT id, name, start_time, end_time, description FROM shifts ORDER BY id")
    shifts = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/attendance_rules.html", {
        "request": request,
        "current_tab": "attendance_rules",
        "rules": rules,
        "shifts": shifts,
        "logged_in_user": username
    })

# 导出考勤数据
@router.get("/export/attendance")
async def export_attendance(
    start_date: str,
    end_date: str,
    department_id: Optional[int] = None,
    format: str = "excel",
    username: str = Depends(get_current_admin_user)
):
    if not xlsxwriter:
        raise HTTPException(
            status_code=500,
            detail="服务器未安装xlsxwriter库，无法导出Excel文件"
        )

    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 构建查询
    query = """
        SELECT p.name, p.employee_id, d.name as department,
               a.timestamp, a.type, a.status, a.note, a.check_type
        FROM attendance a
        JOIN persons p ON a.person_id = p.id
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE date(a.timestamp) BETWEEN ? AND ?
    """
    params = [start_date, end_date]

    if department_id:
        query += " AND p.department_id = ?"
        params.append(department_id)

    query += " ORDER BY p.name, a.timestamp"

    c.execute(query, params)
    records = c.fetchall()

    # 生成Excel文件
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet("考勤数据")

    # 设置表头
    headers = ["姓名", "工号", "部门", "时间", "打卡类型", "类型", "状态", "备注"]
    for col, header in enumerate(headers):
        worksheet.write(0, col, header)

    # 填充数据
    for row, record in enumerate(records, 1):
        worksheet.write(row, 0, record["name"])
        worksheet.write(row, 1, record["employee_id"] or "")
        worksheet.write(row, 2, record["department"] or "")
        worksheet.write(row, 3, record["timestamp"])

        # 打卡类型处理
        check_type_text = ""
        if record["check_type"] == "check_in":
            check_type_text = "上班打卡"
        elif record["check_type"] == "check_out":
            check_type_text = "下班打卡"
        elif record["check_type"] == "unknown":
            check_type_text = "未知类型"
        worksheet.write(row, 4, check_type_text)

        worksheet.write(row, 5, record["type"] or "")
        worksheet.write(row, 6, record["status"] or "")
        worksheet.write(row, 7, record["note"] or "")

    workbook.close()
    output.seek(0)

    # 生成文件名 - 使用纯英文文件名避免编码问题
    filename = f"attendance_data_{start_date}_to_{end_date}"
    if department_id:
        c.execute("SELECT name FROM departments WHERE id=?", (department_id,))
        dept = c.fetchone()
        if dept:
            # 将中文部门名替换为部门ID
            filename += f"_dept{department_id}"
    filename += ".xlsx"

    conn.close()

    return StreamingResponse(
        output,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={
            "Content-Disposition": f"attachment; filename={filename}"
        }
    )

# 统计报表路由
@router.get("/reports")
async def manage_reports(
    request: Request,
    username: str = Depends(get_current_admin_user),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    department_id: Optional[str] = None
):
    # 处理department_id为空字符串的情况
    if department_id == "":
        department_id = None
    elif department_id is not None:
        try:
            department_id = int(department_id)
        except ValueError:
            department_id = None
    # 默认显示最近30天
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")

    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取部门列表
    c.execute("SELECT id, name FROM departments ORDER BY name")
    departments = c.fetchall()

    # 部门考勤统计
    dept_stats_query = """
        SELECT d.name as department_name, COUNT(DISTINCT p.id) as person_count,
               SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count,
               SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late_count,
               SUM(CASE WHEN a.status = 'early_leave' THEN 1 ELSE 0 END) as early_count,
               SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_count
        FROM departments d
        LEFT JOIN persons p ON p.department_id = d.id
        LEFT JOIN attendance a ON a.person_id = p.id AND date(a.date) BETWEEN ? AND ?
    """

    params = [start_date, end_date]

    if department_id:
        dept_stats_query += " WHERE d.id = ?"
        params.append(department_id)

    dept_stats_query += " GROUP BY d.id ORDER BY d.name"

    c.execute(dept_stats_query, params)
    dept_stats = c.fetchall()

    # 个人考勤统计
    person_stats_query = """
        SELECT p.name as person_name, d.name as department_name,
               COUNT(DISTINCT a.date) as total_days,
               SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_days,
               AVG(CASE WHEN a.status = 'late' THEN 30 ELSE 0 END) as avg_late_minutes
        FROM persons p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN attendance a ON a.person_id = p.id AND a.date BETWEEN ? AND ?
    """

    params = [start_date, end_date]

    if department_id:
        person_stats_query += " WHERE p.department_id = ?"
        params.append(department_id)

    person_stats_query += " GROUP BY p.id ORDER BY normal_days DESC, avg_late_minutes ASC LIMIT 10"

    c.execute(person_stats_query, params)
    person_stats = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/reports.html", {
        "request": request,
        "current_tab": "reports",
        "departments": departments,
        "dept_stats": dept_stats,
        "person_stats": person_stats,
        "start_date": start_date,
        "end_date": end_date,
        "selected_department": department_id,
        "logged_in_user": username
    })

# 通知公告路由
@router.get("/announcements")
async def manage_announcements(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取公告列表
    c.execute("""
        SELECT a.*, u.username as creator_name
        FROM announcements a
        LEFT JOIN users u ON a.created_by = u.id
        ORDER BY a.priority DESC, a.created_at DESC
    """)
    announcements = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/announcements.html", {
        "request": request,
        "current_tab": "announcements",
        "announcements": announcements,
        "logged_in_user": username
    })

@router.get("/announcements/new")
async def new_announcement_form(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    return templates.TemplateResponse("admin/announcement_form.html", {
        "request": request,
        "current_tab": "announcements",
        "announcement": None,
        "logged_in_user": username
    })

@router.post("/announcements")
async def create_announcement(
    request: Request,
    username: str = Depends(get_current_admin_user),
    title: str = Form(...),
    content: str = Form(...),
    type: str = Form("normal"),
    priority: int = Form(0),
    start_date: Optional[str] = Form(None),
    end_date: Optional[str] = Form(None),
    status: str = Form("active")
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 获取当前用户ID
        c.execute("SELECT id FROM users WHERE username=?", (username,))
        user_id = c.fetchone()[0]

        # 创建公告
        c.execute("""
            INSERT INTO announcements
            (title, content, type, priority, start_date, end_date, status, created_by, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
        """, (title, content, type, priority, start_date, end_date, status, user_id))

        conn.commit()
        return RedirectResponse(url="/admin/announcements", status_code=303)
    except Exception as e:
        conn.rollback()
        return templates.TemplateResponse("admin/announcement_form.html", {
            "request": request,
            "current_tab": "announcements",
            "announcement": {
                "title": title,
                "content": content,
                "type": type,
                "priority": priority,
                "start_date": start_date,
                "end_date": end_date,
                "status": status
            },
            "error": f"创建公告失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/announcements/{announcement_id}/edit")
async def edit_announcement_form(
    request: Request,
    announcement_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取公告信息
    c.execute("SELECT * FROM announcements WHERE id=?", (announcement_id,))
    announcement = c.fetchone()

    if not announcement:
        conn.close()
        raise HTTPException(status_code=404, detail="公告不存在")

    conn.close()

    return templates.TemplateResponse("admin/announcement_form.html", {
        "request": request,
        "current_tab": "announcements",
        "announcement": announcement,
        "logged_in_user": username
    })

@router.post("/announcements/{announcement_id}")
async def update_announcement(
    request: Request,
    announcement_id: int,
    username: str = Depends(get_current_admin_user),
    title: str = Form(...),
    content: str = Form(...),
    type: str = Form("normal"),
    priority: int = Form(0),
    start_date: Optional[str] = Form(None),
    end_date: Optional[str] = Form(None),
    status: str = Form("active")
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 更新公告
        c.execute("""
            UPDATE announcements
            SET title=?, content=?, type=?, priority=?, start_date=?, end_date=?, status=?, updated_at=datetime('now', 'localtime')
            WHERE id=?
        """, (title, content, type, priority, start_date, end_date, status, announcement_id))

        conn.commit()
        return RedirectResponse(url="/admin/announcements", status_code=303)
    except Exception as e:
        conn.rollback()
        return templates.TemplateResponse("admin/announcement_form.html", {
            "request": request,
            "current_tab": "announcements",
            "announcement": {
                "id": announcement_id,
                "title": title,
                "content": content,
                "type": type,
                "priority": priority,
                "start_date": start_date,
                "end_date": end_date,
                "status": status
            },
            "error": f"更新公告失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/announcements/{announcement_id}/delete")
async def delete_announcement(
    announcement_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 删除公告
        c.execute("DELETE FROM announcements WHERE id=?", (announcement_id,))
        conn.commit()

        return RedirectResponse(url="/admin/announcements", status_code=303)
    except Exception as e:
        conn.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除公告失败: {str(e)}"
        )
    finally:
        conn.close()

# 系统设置路由
@router.get("/settings")
async def manage_settings(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 检查settings表是否存在
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'")
    if not c.fetchone():
        # 创建settings表
        c.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 插入默认设置
        default_settings = [
            ('face_recognition_threshold', '0.4', '人脸识别阈值'),
            ('attendance_time_range', '30', '考勤时间范围(分钟)'),
            ('enable_email_notification', '0', '启用邮件通知'),
            ('smtp_settings', '{"server": "", "port": 587, "username": "", "password": ""}', 'SMTP设置'),
            ('system_name', '人脸识别考勤系统', '系统名称'),
            ('system_logo', '', '系统Logo URL'),
            ('data_retention_days', '365', '数据保留天数')
        ]

        for key, value, description in default_settings:
            c.execute(
                "INSERT INTO settings (key, value, description) VALUES (?, ?, ?)",
                (key, value, description)
            )

        conn.commit()

    # 获取系统设置
    c.execute("SELECT id, key, value, description FROM settings ORDER BY id")
    settings = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/settings.html", {
        "request": request,
        "current_tab": "settings",
        "settings": settings,
        "logged_in_user": username
    })

@router.post("/settings")
async def update_settings(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    form_data = await request.form()
    settings = {}

    # 提取表单中的设置项
    for key, value in form_data.items():
        if key.startswith("setting_"):
            setting_key = key[8:]  # 去掉"setting_"前缀
            settings[setting_key] = value

    # 处理复选框设置项（未选中的复选框不会出现在表单数据中）
    checkbox_settings = ['enable_email_notification', 'disable_time_check']
    for checkbox_key in checkbox_settings:
        if checkbox_key not in settings:
            # 复选框未选中，设置为相应的false值
            if checkbox_key == 'enable_email_notification':
                settings[checkbox_key] = '0'
            elif checkbox_key == 'disable_time_check':
                settings[checkbox_key] = 'false'

    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 更新设置
        for key, value in settings.items():
            c.execute("""
                UPDATE settings
                SET value=?, updated_at=datetime('now', 'localtime')
                WHERE key=?
            """, (value, key))

        conn.commit()

        # 获取更新后的设置
        c.execute("SELECT key, value, description FROM settings ORDER BY id")
        updated_settings = c.fetchall()

        return templates.TemplateResponse("admin/settings.html", {
            "request": request,
            "current_tab": "settings",
            "settings": updated_settings,
            "success": "设置已成功更新",
            "logged_in_user": username
        })
    except Exception as e:
        conn.rollback()

        # 获取原始设置
        c.execute("SELECT key, value, description FROM settings ORDER BY id")
        settings = c.fetchall()

        return templates.TemplateResponse("admin/settings.html", {
            "request": request,
            "current_tab": "settings",
            "settings": settings,
            "error": f"更新设置失败: {str(e)}",
            "logged_in_user": username
        })
    finally:
        conn.close()

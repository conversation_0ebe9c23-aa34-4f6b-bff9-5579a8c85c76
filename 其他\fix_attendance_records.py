#!/usr/bin/env python3
import sqlite3
import random
import os
from datetime import datetime, timedelta

# 可能的数据库路径
DATABASE_FILES = ["database.db", "face_recognition.db", "attendance.db"]

def fix_attendance_records():
    """修复考勤记录中的check_type字段并调整日期到5月份"""
    print("=== 开始修复考勤记录 ===")

    # 检查所有可能的数据库文件
    for db_file in DATABASE_FILES:
        if not os.path.exists(db_file):
            print(f"数据库文件 {db_file} 不存在")
            continue

        print(f"尝试连接数据库 {db_file}...")
        conn = sqlite3.connect(db_file)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # 检查数据库中是否有attendance表
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='attendance'")
        if not c.fetchone():
            print(f"数据库 {db_file} 中没有attendance表")
            conn.close()
            continue

        print(f"在数据库 {db_file} 中找到attendance表")

        # 1. 查询所有考勤记录
        c.execute("""
            SELECT id, timestamp, check_type, type, status
            FROM attendance
            ORDER BY timestamp
        """)
        records = c.fetchall()

        if not records:
            print(f"数据库 {db_file} 中没有找到考勤记录")
            conn.close()
            continue

        print(f"找到 {len(records)} 条考勤记录")

        # 2. 生成5月份的日期范围（1-19号）
        may_dates = []
        for day in range(1, 20):
            date_str = f"2023-05-{day:02d}"
            may_dates.append(date_str)

        # 3. 更新每条记录
        for i, record in enumerate(records):
            record_id = record['id']
            old_timestamp = record['timestamp'] if record['timestamp'] else "2023-01-01 12:00:00"
            old_check_type = record['check_type'] if record['check_type'] else "unknown"

            # 解析时间戳
            try:
                dt = datetime.strptime(old_timestamp, "%Y-%m-%d %H:%M:%S")
                time_part = dt.strftime("%H:%M:%S")
            except ValueError:
                # 如果时间戳格式不正确，使用默认时间
                time_part = "12:00:00"

            # 选择一个5月份的日期
            may_date = random.choice(may_dates)

            # 创建新的时间戳
            new_timestamp = f"{may_date} {time_part}"

            # 根据时间确定check_type
            hour = int(time_part.split(':')[0])
            # 无论原来的check_type是什么，都根据时间重新设置
            if hour < 12:
                new_check_type = 'check_in'
            else:
                new_check_type = 'check_out'

            # 更新记录
            c.execute("""
                UPDATE attendance
                SET timestamp = ?, check_type = ?
                WHERE id = ?
            """, (new_timestamp, new_check_type, record_id))

            print(f"更新记录 {record_id}: {old_timestamp} -> {new_timestamp}, {old_check_type} -> {new_check_type}")

        # 提交更改
        conn.commit()
        print(f"成功更新 {len(records)} 条考勤记录")

        # 4. 验证更新结果
        c.execute("""
            SELECT check_type, COUNT(*) as count
            FROM attendance
            GROUP BY check_type
        """)
        type_counts = c.fetchall()

        print("\n=== 更新后的考勤类型统计 ===")
        for type_count in type_counts:
            print(f"{type_count['check_type']}: {type_count['count']} 条记录")

        conn.close()

        # 已经找到并处理了一个数据库，退出循环
        return

    # 如果执行到这里，说明没有找到有效的数据库
    print("未找到包含考勤记录的数据库")

if __name__ == "__main__":
    fix_attendance_records()

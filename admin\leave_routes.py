#!/usr/bin/env python3
import sqlite3
import os
from datetime import datetime, timedelta
from typing import Optional, List

from fastapi import APIRouter, Request, Depends, HTTPException, Form, File, UploadFile, Query
from fastapi.templating import Jinja2Templates
from fastapi.responses import RedirectResponse, FileResponse, JSONResponse

from admin.db_init import DATABASE
from admin.auth import get_current_admin_user

router = APIRouter(prefix="/admin/leave")
templates = Jinja2Templates(directory="templates")

# 请假管理列表页面
@router.get("")
async def leave_management(
    request: Request,
    status: Optional[str] = None,
    department_id: Optional[str] = None,  # 改为字符串类型以处理"全部"选项
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: str = Depends(get_current_admin_user)
):
    # 默认显示最近30天
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    if not end_date:
        end_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")

    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取部门列表
    c.execute("SELECT id, name FROM departments ORDER BY id")
    departments = c.fetchall()

    # 构建查询条件
    query = """
        SELECT lr.*, lt.name as type_name, lt.color, p.name as person_name, d.name as department_name
        FROM leave_requests lr
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        JOIN persons p ON lr.person_id = p.id
        JOIN departments d ON p.department_id = d.id
        WHERE 1=1
    """
    params = []

    if status:
        query += " AND lr.status = ?"
        params.append(status)

    # 处理部门查询，如果不是"全部"且不为空，则添加部门过滤条件
    if department_id and department_id != "all" and department_id.strip():
        try:
            dept_id = int(department_id)
            query += " AND p.department_id = ?"
            params.append(dept_id)
        except ValueError:
            # 如果转换失败，忽略部门过滤
            pass

    if start_date and end_date:
        query += " AND (lr.start_date >= ? OR lr.end_date <= ?)"
        params.extend([start_date, end_date])

    query += " ORDER BY lr.created_at DESC"

    # 执行查询
    c.execute(query, params)
    leave_requests = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/leave_management.html", {
        "request": request,
        "current_tab": "leave",
        "leave_requests": leave_requests,
        "departments": departments,
        "status": status,
        "department_id": department_id,
        "start_date": start_date,
        "end_date": end_date,
        "logged_in_user": current_user
    })

# 请假详情页面
@router.get("/{leave_id}")
async def leave_detail(
    request: Request,
    leave_id: int,
    current_user: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取请假申请详情
    c.execute("""
        SELECT lr.*, lt.name as type_name, lt.color
        FROM leave_requests lr
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        WHERE lr.id = ?
    """, (leave_id,))
    leave_request = c.fetchone()

    if not leave_request:
        conn.close()
        raise HTTPException(status_code=404, detail="未找到请假申请")

    # 获取申请人信息
    c.execute("""
        SELECT p.*, d.name as department_name
        FROM persons p
        JOIN departments d ON p.department_id = d.id
        WHERE p.id = ?
    """, (leave_request['person_id'],))
    person = c.fetchone()

    # 获取审批记录
    c.execute("""
        SELECT al.*, u.username as approver_name
        FROM leave_approval_logs al
        JOIN users u ON al.approver_id = u.id
        WHERE al.request_id = ?
        ORDER BY al.created_at
    """, (leave_id,))
    approval_logs = c.fetchall()

    # 获取申请人历史请假记录
    c.execute("""
        SELECT lr.id, lr.start_date, lr.end_date, lr.days, lr.status, lr.created_at,
               lt.name as type_name, lt.color
        FROM leave_requests lr
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        WHERE lr.person_id = ? AND lr.id != ?
        ORDER BY lr.created_at DESC
        LIMIT 5
    """, (leave_request['person_id'], leave_id))
    person_leave_history = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/leave_detail.html", {
        "request": request,
        "current_tab": "leave",
        "leave_request": leave_request,
        "person": person,
        "approval_logs": approval_logs,
        "person_leave_history": person_leave_history,
        "logged_in_user": current_user
    })

# 批准请假申请
@router.post("/{leave_id}/approve")
async def approve_leave(
    request: Request,
    leave_id: int,
    comments: Optional[str] = Form(None),
    current_user: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 获取当前用户ID
        c.execute("SELECT id FROM users WHERE username = ?", (current_user,))
        user = c.fetchone()
        if not user:
            conn.close()
            raise HTTPException(status_code=404, detail="未找到用户信息")

        user_id = user[0]

        # 检查请假申请是否存在且状态为待审批
        c.execute("SELECT status FROM leave_requests WHERE id = ?", (leave_id,))
        leave_request = c.fetchone()

        if not leave_request:
            conn.close()
            raise HTTPException(status_code=404, detail="未找到请假申请")

        if leave_request[0] != 'pending':
            conn.close()
            raise HTTPException(status_code=400, detail="只能审批待审批状态的请假申请")

        # 更新请假申请状态为已批准
        c.execute("""
            UPDATE leave_requests
            SET status = 'approved', approver_id = ?, approved_at = datetime('now', 'localtime')
            WHERE id = ?
        """, (user_id, leave_id))

        # 更新审批记录
        c.execute("""
            UPDATE leave_approval_logs
            SET status = 'approved', comments = ?, created_at = datetime('now', 'localtime')
            WHERE request_id = ? AND approver_id = ?
        """, (comments, leave_id, user_id))

        # 如果没有找到审批记录，创建一个新的
        if c.rowcount == 0:
            c.execute("""
                INSERT INTO leave_approval_logs (request_id, approver_id, status, comments, created_at)
                VALUES (?, ?, 'approved', ?, datetime('now', 'localtime'))
            """, (leave_id, user_id, comments))

        conn.commit()

        return RedirectResponse(url=f"/admin/leave/{leave_id}", status_code=303)
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"批准请假申请失败: {str(e)}")
    finally:
        conn.close()

# 拒绝请假申请
@router.post("/{leave_id}/reject")
async def reject_leave(
    request: Request,
    leave_id: int,
    rejection_reason: str = Form(...),
    current_user: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 获取当前用户ID
        c.execute("SELECT id FROM users WHERE username = ?", (current_user,))
        user = c.fetchone()
        if not user:
            conn.close()
            raise HTTPException(status_code=404, detail="未找到用户信息")

        user_id = user[0]

        # 检查请假申请是否存在且状态为待审批
        c.execute("SELECT status FROM leave_requests WHERE id = ?", (leave_id,))
        leave_request = c.fetchone()

        if not leave_request:
            conn.close()
            raise HTTPException(status_code=404, detail="未找到请假申请")

        if leave_request[0] != 'pending':
            conn.close()
            raise HTTPException(status_code=400, detail="只能审批待审批状态的请假申请")

        # 更新请假申请状态为已拒绝
        c.execute("""
            UPDATE leave_requests
            SET status = 'rejected', approver_id = ?, approved_at = datetime('now', 'localtime'), rejection_reason = ?
            WHERE id = ?
        """, (user_id, rejection_reason, leave_id))

        # 更新审批记录
        c.execute("""
            UPDATE leave_approval_logs
            SET status = 'rejected', comments = ?, created_at = datetime('now', 'localtime')
            WHERE request_id = ? AND approver_id = ?
        """, (rejection_reason, leave_id, user_id))

        # 如果没有找到审批记录，创建一个新的
        if c.rowcount == 0:
            c.execute("""
                INSERT INTO leave_approval_logs (request_id, approver_id, status, comments, created_at)
                VALUES (?, ?, 'rejected', ?, datetime('now', 'localtime'))
            """, (leave_id, user_id, rejection_reason))

        conn.commit()

        return RedirectResponse(url=f"/admin/leave/{leave_id}", status_code=303)
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"拒绝请假申请失败: {str(e)}")
    finally:
        conn.close()

# 查看附件
@router.get("/{leave_id}/attachment")
async def leave_attachment(
    request: Request,
    leave_id: int,
    current_user: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取请假申请附件路径
    c.execute("""
        SELECT attachment_path
        FROM leave_requests
        WHERE id = ?
    """, (leave_id,))
    result = c.fetchone()

    conn.close()

    if not result or not result['attachment_path']:
        raise HTTPException(status_code=404, detail="未找到附件")

    attachment_path = result['attachment_path']

    if not os.path.exists(attachment_path):
        raise HTTPException(status_code=404, detail="附件文件不存在")

    return FileResponse(attachment_path)

# 请假类型管理页面
@router.get("/types")
async def leave_types(
    request: Request,
    current_user: str = Depends(get_current_admin_user)
):
    """请假类型管理页面"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # 获取请假类型列表
    c.execute("""
        SELECT id, name, description, color, require_attachment, max_days, status
        FROM leave_types
        ORDER BY id
    """)
    leave_types = c.fetchall()

    conn.close()

    return templates.TemplateResponse("admin/leave_types.html", {
        "request": request,
        "current_tab": "leave",
        "leave_types": leave_types,
        "logged_in_user": current_user
    })

# 添加请假类型
@router.post("/types/add")
async def add_leave_type(
    request: Request,
    name: str = Form(...),
    description: Optional[str] = Form(None),
    color: str = Form(...),
    require_attachment: Optional[int] = Form(0),
    max_days: int = Form(0),
    status: Optional[int] = Form(0),
    current_user: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查名称是否已存在
        c.execute("SELECT id FROM leave_types WHERE name = ?", (name,))
        if c.fetchone():
            conn.close()
            return templates.TemplateResponse("admin/leave_types.html", {
                "request": request,
                "current_tab": "leave",
                "error": f"请假类型 '{name}' 已存在",
                "logged_in_user": current_user
            })

        # 插入请假类型
        c.execute("""
            INSERT INTO leave_types (name, description, color, require_attachment, max_days, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))
        """, (name, description, color, require_attachment, max_days, status))

        conn.commit()

        # 获取请假类型列表
        c.execute("""
            SELECT id, name, description, color, require_attachment, max_days, status
            FROM leave_types
            ORDER BY id
        """)
        leave_types = c.fetchall()

        return templates.TemplateResponse("admin/leave_types.html", {
            "request": request,
            "current_tab": "leave",
            "leave_types": leave_types,
            "success": f"请假类型 '{name}' 添加成功",
            "logged_in_user": current_user
        })
    except Exception as e:
        conn.rollback()

        # 获取请假类型列表
        c.execute("""
            SELECT id, name, description, color, require_attachment, max_days, status
            FROM leave_types
            ORDER BY id
        """)
        leave_types = c.fetchall()

        return templates.TemplateResponse("admin/leave_types.html", {
            "request": request,
            "current_tab": "leave",
            "leave_types": leave_types,
            "error": f"添加请假类型失败: {str(e)}",
            "logged_in_user": current_user
        })
    finally:
        conn.close()

# 编辑请假类型
@router.post("/types/{type_id}/edit")
async def edit_leave_type(
    request: Request,
    type_id: int,
    name: str = Form(...),
    description: Optional[str] = Form(None),
    color: str = Form(...),
    require_attachment: Optional[int] = Form(0),
    max_days: int = Form(0),
    status: Optional[int] = Form(0),
    current_user: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查请假类型是否存在
        c.execute("SELECT id FROM leave_types WHERE id = ?", (type_id,))
        if not c.fetchone():
            conn.close()
            return templates.TemplateResponse("admin/leave_types.html", {
                "request": request,
                "current_tab": "leave",
                "error": f"请假类型 ID {type_id} 不存在",
                "logged_in_user": current_user
            })

        # 检查名称是否已被其他类型使用
        c.execute("SELECT id FROM leave_types WHERE name = ? AND id != ?", (name, type_id))
        if c.fetchone():
            conn.close()
            return templates.TemplateResponse("admin/leave_types.html", {
                "request": request,
                "current_tab": "leave",
                "error": f"请假类型名称 '{name}' 已被使用",
                "logged_in_user": current_user
            })

        # 更新请假类型
        c.execute("""
            UPDATE leave_types
            SET name = ?, description = ?, color = ?, require_attachment = ?, max_days = ?, status = ?
            WHERE id = ?
        """, (name, description, color, require_attachment, max_days, status, type_id))

        conn.commit()

        # 获取请假类型列表
        c.execute("""
            SELECT id, name, description, color, require_attachment, max_days, status
            FROM leave_types
            ORDER BY id
        """)
        leave_types = c.fetchall()

        return templates.TemplateResponse("admin/leave_types.html", {
            "request": request,
            "current_tab": "leave",
            "leave_types": leave_types,
            "success": f"请假类型 '{name}' 更新成功",
            "logged_in_user": current_user
        })
    except Exception as e:
        conn.rollback()

        # 获取请假类型列表
        c.execute("""
            SELECT id, name, description, color, require_attachment, max_days, status
            FROM leave_types
            ORDER BY id
        """)
        leave_types = c.fetchall()

        return templates.TemplateResponse("admin/leave_types.html", {
            "request": request,
            "current_tab": "leave",
            "leave_types": leave_types,
            "error": f"更新请假类型失败: {str(e)}",
            "logged_in_user": current_user
        })
    finally:
        conn.close()

# 删除请假类型
@router.post("/types/{type_id}/delete")
async def delete_leave_type(
    request: Request,
    type_id: int,
    current_user: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()

        # 检查请假类型是否存在
        c.execute("SELECT name FROM leave_types WHERE id = ?", (type_id,))
        result = c.fetchone()
        if not result:
            conn.close()
            return templates.TemplateResponse("admin/leave_types.html", {
                "request": request,
                "current_tab": "leave",
                "error": f"请假类型 ID {type_id} 不存在",
                "logged_in_user": current_user
            })

        type_name = result[0]

        # 检查是否有关联的请假申请
        c.execute("SELECT COUNT(*) FROM leave_requests WHERE leave_type_id = ?", (type_id,))
        count = c.fetchone()[0]

        if count > 0:
            conn.close()
            return templates.TemplateResponse("admin/leave_types.html", {
                "request": request,
                "current_tab": "leave",
                "error": f"请假类型 '{type_name}' 已被 {count} 个请假申请使用，无法删除",
                "logged_in_user": current_user
            })

        # 删除请假类型
        c.execute("DELETE FROM leave_types WHERE id = ?", (type_id,))

        conn.commit()

        # 获取请假类型列表
        c.execute("""
            SELECT id, name, description, color, require_attachment, max_days, status
            FROM leave_types
            ORDER BY id
        """)
        leave_types = c.fetchall()

        return templates.TemplateResponse("admin/leave_types.html", {
            "request": request,
            "current_tab": "leave",
            "leave_types": leave_types,
            "success": f"请假类型 '{type_name}' 删除成功",
            "logged_in_user": current_user
        })
    except Exception as e:
        conn.rollback()

        # 获取请假类型列表
        c.execute("""
            SELECT id, name, description, color, require_attachment, max_days, status
            FROM leave_types
            ORDER BY id
        """)
        leave_types = c.fetchall()

        return templates.TemplateResponse("admin/leave_types.html", {
            "request": request,
            "current_tab": "leave",
            "leave_types": leave_types,
            "error": f"删除请假类型失败: {str(e)}",
            "logged_in_user": current_user
        })
    finally:
        conn.close()

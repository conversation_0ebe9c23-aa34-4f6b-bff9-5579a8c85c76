#!/usr/bin/env python3
import sqlite3
import random
from datetime import datetime

# 数据库路径
DATABASE = "face_recognition.db"

def fix_attendance_dates():
    """修复考勤记录日期并为user1添加指定的考勤记录"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    print("=== 开始修复考勤记录日期 ===")
    
    # 1. 将所有考勤记录的年份从2023改为2025
    c.execute("""
        UPDATE attendance
        SET timestamp = REPLACE(timestamp, '2023', '2025')
    """)
    
    print(f"已将所有考勤记录的年份从2023改为2025，影响行数: {conn.total_changes}")
    
    # 2. 查找user1关联的person_id
    user1_person_id = None
    
    # 先查找用户ID
    c.execute("SELECT id FROM users WHERE username = 'user1'")
    user_result = c.fetchone()
    
    if user_result:
        user_id = user_result['id']
        # 查找关联的人员ID
        c.execute("""
            SELECT person_id FROM user_person_relations
            WHERE user_id = ?
        """, (user_id,))
        relation = c.fetchone()
        if relation:
            user1_person_id = relation['person_id']
    
    # 如果没找到关联，尝试通过名称查找
    if not user1_person_id:
        c.execute("SELECT id FROM persons WHERE name = 'user1' OR employee_id = 'user1'")
        person = c.fetchone()
        if person:
            user1_person_id = person['id']
    
    # 如果还是没找到，创建一个新的人员记录
    if not user1_person_id:
        print("未找到user1关联的人员记录，创建新记录...")
        c.execute("""
            INSERT INTO persons (name, employee_id, position)
            VALUES ('user1', 'EMP001', '员工')
        """)
        user1_person_id = c.lastrowid
        print(f"已创建新的人员记录，ID: {user1_person_id}")
        
        # 如果user1存在，创建关联
        if user_result:
            c.execute("""
                INSERT INTO user_person_relations (user_id, person_id)
                VALUES (?, ?)
            """, (user_id, user1_person_id))
            print(f"已创建用户与人员的关联")
    
    # 3. 删除user1的所有考勤记录
    c.execute("DELETE FROM attendance WHERE person_id = ?", (user1_person_id,))
    print(f"已删除user1的所有考勤记录，影响行数: {conn.total_changes}")
    
    # 4. 为user1添加指定的考勤记录
    attendance_records = [
        {
            "timestamp": "2025-04-09 10:13:00",
            "check_type": "check_in",
            "status": "normal",
            "note": None
        },
        {
            "timestamp": "2025-04-09 19:56:00",
            "check_type": "check_out",
            "status": "normal",
            "note": None
        },
        {
            "timestamp": "2025-04-10 10:30:00",
            "check_type": "check_in",
            "status": "late",
            "note": "交通拥堵"
        },
        {
            "timestamp": "2025-04-10 19:16:00",
            "check_type": "check_out",
            "status": "normal",
            "note": None
        }
    ]
    
    for record in attendance_records:
        c.execute("""
            INSERT INTO attendance
            (person_id, timestamp, check_type, status, note, type)
            VALUES (?, ?, ?, ?, ?, 'auto')
        """, (
            user1_person_id,
            record["timestamp"],
            record["check_type"],
            record["status"],
            record["note"]
        ))
        print(f"已添加考勤记录: {record['timestamp']}, {record['check_type']}, {record['status']}")
    
    # 提交更改
    conn.commit()
    print("所有更改已提交到数据库")
    
    # 验证user1的考勤记录
    c.execute("""
        SELECT timestamp, check_type, status, note
        FROM attendance
        WHERE person_id = ?
        ORDER BY timestamp
    """, (user1_person_id,))
    records = c.fetchall()
    
    print("\n=== user1的考勤记录 ===")
    for record in records:
        print(f"时间: {record['timestamp']}, 类型: {record['check_type']}, 状态: {record['status']}, 备注: {record['note']}")
    
    conn.close()

if __name__ == "__main__":
    fix_attendance_dates()

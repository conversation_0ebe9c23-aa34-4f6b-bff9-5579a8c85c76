{% extends "admin/base.html" %}

{% block title %}部门管理{% endblock %}

{% block page_title %}部门管理{% endblock %}

{% block page_actions %}
<a href="/admin/departments/new" class="btn btn-success">
    <i class="bi bi-plus-lg"></i> 新增部门
</a>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">部门列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>部门名称</th>
                        <th>上级部门</th>
                        <th>部门经理</th>
                        <th>描述</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for dept in departments %}
                    <tr>
                        <td>{{ dept.id }}</td>
                        <td>{{ dept.name }}</td>
                        <td>{{ dept.parent_name if dept.parent_name else '-' }}</td>
                        <td>{{ dept.manager_name if dept.manager_name else '-' }}</td>
                        <td>{{ dept.description if dept.description else '-' }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="/admin/departments/{{ dept.id }}/edit" class="btn btn-sm btn-primary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ dept.id }}" data-name="{{ dept.name }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除部门 <span id="deptName" class="fw-bold"></span> 吗？</p>
                <p class="text-danger">此操作不可恢复。如果部门下有子部门或关联人员，将无法删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 初始化DataTable
        if (!$.fn.DataTable.isDataTable('.datatable')) {
            $('.datatable').DataTable();
        }

        // 删除确认 - 使用事件委托处理分页后的元素
        $(document).on('click', '.delete-btn', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            $('#deptName').text(name);
            $('#deleteForm').attr('action', `/admin/departments/${id}/delete`);

            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
    });
</script>
{% endblock %}
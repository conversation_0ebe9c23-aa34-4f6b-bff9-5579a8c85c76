{% extends "admin/base.html" %}

{% block title %}请假管理{% endblock %}

{% block page_title %}请假管理{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">请假申请筛选</h5>
            </div>
            <div class="card-body">
                <form method="get" action="/admin/leave" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部</option>
                            <option value="pending" {% if status == 'pending' %}selected{% endif %}>待审批</option>
                            <option value="approved" {% if status == 'approved' %}selected{% endif %}>已批准</option>
                            <option value="rejected" {% if status == 'rejected' %}selected{% endif %}>已拒绝</option>
                            <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>已取消</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="department_id" class="form-label">部门</label>
                        <select class="form-select" id="department_id" name="department_id">
                            <option value="">全部</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if department_id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <a href="/admin/leave" class="btn btn-secondary">
                            <i class="bi bi-arrow-counterclockwise"></i> 重置
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">请假申请列表</h5>
    </div>
    <div class="card-body">
        {% if leave_requests %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>申请人</th>
                        <th>部门</th>
                        <th>请假类型</th>
                        <th>开始日期</th>
                        <th>结束日期</th>
                        <th>天数</th>
                        <th>申请时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in leave_requests %}
                    <tr>
                        <td>{{ request.id }}</td>
                        <td>{{ request.person_name }}</td>
                        <td>{{ request.department_name }}</td>
                        <td>
                            <span class="badge" style="background-color: {{ request.color }};">{{ request.type_name }}</span>
                        </td>
                        <td>{{ request.start_date }}{% if request.start_time %} {{ request.start_time }}{% endif %}</td>
                        <td>{{ request.end_date }}{% if request.end_time %} {{ request.end_time }}{% endif %}</td>
                        <td>{{ request.days }}</td>
                        <td>{{ request.created_at }}</td>
                        <td>
                            {% if request.status == 'pending' %}
                            <span class="badge bg-warning">待审批</span>
                            {% elif request.status == 'approved' %}
                            <span class="badge bg-success">已批准</span>
                            {% elif request.status == 'rejected' %}
                            <span class="badge bg-danger">已拒绝</span>
                            {% elif request.status == 'cancelled' %}
                            <span class="badge bg-secondary">已取消</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="/admin/leave/{{ request.id }}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if request.status == 'pending' %}
                                <button type="button" class="btn btn-sm btn-success approve-btn" data-id="{{ request.id }}">
                                    <i class="bi bi-check-circle"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger reject-btn" data-id="{{ request.id }}">
                                    <i class="bi bi-x-circle"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            没有找到符合条件的请假申请。
        </div>
        {% endif %}
    </div>
</div>

<!-- 批准确认模态框 -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批准请假申请</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="approveForm" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="approve_comments" class="form-label">批准意见 (可选)</label>
                        <textarea class="form-control" id="approve_comments" name="comments" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">批准</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 拒绝确认模态框 -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">拒绝请假申请</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="rejectForm" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">拒绝原因 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">拒绝</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 批准请假确认
        const approveModal = new bootstrap.Modal(document.getElementById('approveModal'));
        const approveButtons = document.querySelectorAll('.approve-btn');
        const approveForm = document.getElementById('approveForm');
        
        approveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                approveForm.action = `/admin/leave/${id}/approve`;
                approveModal.show();
            });
        });
        
        // 拒绝请假确认
        const rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
        const rejectButtons = document.querySelectorAll('.reject-btn');
        const rejectForm = document.getElementById('rejectForm');
        
        rejectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                rejectForm.action = `/admin/leave/${id}/reject`;
                rejectModal.show();
            });
        });
    });
</script>
{% endblock %}

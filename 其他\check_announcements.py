#!/usr/bin/env python3
import sqlite3
from datetime import datetime
import os

# 数据库路径
DATABASE = "attendance.db"

def create_announcements_table():
    """创建通知公告表（如果不存在）"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()

    # 检查表是否存在
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='announcements'")
    if not c.fetchone():
        print("创建 announcements 表...")
        c.execute('''
            CREATE TABLE announcements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                type TEXT DEFAULT 'normal',
                priority INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active',
                start_date TEXT,
                end_date TEXT,
                created_by INTEGER,
                created_at TEXT,
                updated_at TEXT,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        ''')
        conn.commit()
        print("announcements 表创建成功")
    else:
        print("announcements 表已存在")

    conn.close()

def check_announcements():
    """检查数据库中的通知公告数据"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    print("=== 所有公告 ===")
    c.execute("""
        SELECT a.id, a.title, a.content, a.type, a.priority, a.status,
               a.start_date, a.end_date, a.created_at, a.created_by, u.username as creator_name
        FROM announcements a
        LEFT JOIN users u ON a.created_by = u.id
        ORDER BY a.priority DESC, a.created_at DESC
    """)
    all_announcements = c.fetchall()

    if not all_announcements:
        print("数据库中没有公告数据")
    else:
        for ann in all_announcements:
            print(f"ID: {ann['id']}, 标题: {ann['title']}, 状态: {ann['status']}, 类型: {ann['type']}")
            print(f"  开始日期: {ann['start_date']}, 结束日期: {ann['end_date']}")
            print(f"  创建者: {ann['creator_name']} (ID: {ann['created_by']})")
            print(f"  创建时间: {ann['created_at']}")
            print(f"  内容: {ann['content'][:50]}..." if len(ann['content']) > 50 else f"  内容: {ann['content']}")
            print("-" * 50)

    print("\n=== 有效公告 (当前日期内且状态为active) ===")
    c.execute("""
        SELECT a.id, a.title, a.type, a.status, a.start_date, a.end_date
        FROM announcements a
        WHERE (a.start_date IS NULL OR date(a.start_date) <= date('now', 'localtime'))
        AND (a.end_date IS NULL OR date(a.end_date) >= date('now', 'localtime'))
        AND a.status = 'active'
        ORDER BY a.priority DESC, a.created_at DESC
    """)
    active_announcements = c.fetchall()

    if not active_announcements:
        print("没有当前有效的公告")
    else:
        for ann in active_announcements:
            print(f"ID: {ann['id']}, 标题: {ann['title']}, 状态: {ann['status']}, 类型: {ann['type']}")
            print(f"  开始日期: {ann['start_date']}, 结束日期: {ann['end_date']}")
            print("-" * 50)

    # 检查日期格式和当前日期
    print("\n=== 日期检查 ===")
    print(f"当前日期: {datetime.now().strftime('%Y-%m-%d')}")
    print(f"当前日期时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 使用单独的变量存储SQL查询结果
    date_result = c.execute("SELECT date('now', 'localtime')").fetchone()[0]
    datetime_result = c.execute("SELECT datetime('now', 'localtime')").fetchone()[0]

    print(f"SQLite日期: {date_result}")
    print(f"SQLite日期时间: {datetime_result}")

    conn.close()

def add_test_announcement():
    """添加测试公告"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()

    # 获取管理员用户ID
    c.execute("SELECT id FROM users WHERE role='admin' LIMIT 1")
    admin_id = c.fetchone()

    if not admin_id:
        print("找不到管理员用户，无法添加测试公告")
        conn.close()
        return

    admin_id = admin_id[0]

    # 添加测试公告
    c.execute("""
        INSERT INTO announcements
        (title, content, type, priority, status, created_by, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
    """, ("测试公告", "这是一条测试公告内容", "normal", 0, "active", admin_id))

    announcement_id = c.lastrowid
    conn.commit()
    conn.close()

    print(f"已添加测试公告，ID: {announcement_id}")

if __name__ == "__main__":
    import sys

    # 首先创建表（如果不存在）
    create_announcements_table()

    if len(sys.argv) > 1 and sys.argv[1] == "--add":
        add_test_announcement()

    check_announcements()

#!/usr/bin/env python3
import sqlite3
import os

# 数据库路径
DATABASE = "attendance.db"

def check_database_structure():
    """检查数据库结构"""
    if not os.path.exists(DATABASE):
        print(f"数据库文件 {DATABASE} 不存在")
        return
    
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    # 获取所有表
    c.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = c.fetchall()
    
    print(f"数据库中的表 ({len(tables)}):")
    for i, table in enumerate(tables, 1):
        table_name = table[0]
        print(f"{i}. {table_name}")
        
        # 获取表结构
        c.execute(f"PRAGMA table_info({table_name})")
        columns = c.fetchall()
        
        print(f"   列数: {len(columns)}")
        for col in columns:
            col_id, col_name, col_type, not_null, default_value, pk = col
            print(f"   - {col_name} ({col_type}){' PRIMARY KEY' if pk else ''}{' NOT NULL' if not_null else ''}")
        
        # 获取行数
        c.execute(f"SELECT COUNT(*) FROM {table_name}")
        row_count = c.fetchone()[0]
        print(f"   行数: {row_count}")
        
        # 如果有数据，显示前5行
        if row_count > 0:
            c.execute(f"SELECT * FROM {table_name} LIMIT 5")
            rows = c.fetchall()
            print(f"   前{min(5, row_count)}行数据:")
            for row in rows:
                print(f"   {row}")
        
        print()
    
    conn.close()

def create_announcements_table():
    """创建通知公告表（如果不存在）"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    # 检查表是否存在
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='announcements'")
    if not c.fetchone():
        print("创建 announcements 表...")
        c.execute('''
            CREATE TABLE announcements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                type TEXT DEFAULT 'normal',
                priority INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active',
                start_date TEXT,
                end_date TEXT,
                created_by INTEGER,
                created_at TEXT,
                updated_at TEXT
            )
        ''')
        conn.commit()
        print("announcements 表创建成功")
    else:
        print("announcements 表已存在")
    
    conn.close()

def add_test_announcement():
    """添加测试公告"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    # 添加测试公告（不依赖于users表）
    c.execute("""
        INSERT INTO announcements
        (title, content, type, priority, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
    """, ("测试公告", "这是一条测试公告内容", "normal", 0, "active"))
    
    announcement_id = c.lastrowid
    conn.commit()
    conn.close()
    
    print(f"已添加测试公告，ID: {announcement_id}")

if __name__ == "__main__":
    import sys
    
    # 检查数据库结构
    check_database_structure()
    
    # 如果指定了--create参数，创建announcements表
    if len(sys.argv) > 1 and sys.argv[1] == "--create":
        create_announcements_table()
    
    # 如果指定了--add参数，添加测试公告
    if len(sys.argv) > 1 and sys.argv[1] == "--add":
        create_announcements_table()  # 确保表存在
        add_test_announcement()

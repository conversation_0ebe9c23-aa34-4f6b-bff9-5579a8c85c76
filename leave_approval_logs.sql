/*
 Navicat Premium Data Transfer

 Source Server         : face_recognition
 Source Server Type    : SQLite
 Source Server Version : 3035005
 Source Schema         : main

 Target Server Type    : SQLite
 Target Server Version : 3035005
 File Encoding         : 65001

 Date: 02/06/2025 23:32:22
*/

PRAGMA foreign_keys = false;

-- ----------------------------
-- Table structure for leave_approval_logs
-- ----------------------------
DROP TABLE IF EXISTS "leave_approval_logs";
CREATE TABLE "leave_approval_logs" (
  "id" INTEGER PRIMARY KEY AUTOINCREMENT,
  "request_id" INTEGER NOT NULL,
  "approver_id" INTEGER NOT NULL,
  "status" TEXT NOT NULL,
  "comments" TEXT,
  "created_at" TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("request_id") REFERENCES "leave_requests" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  FOREIGN KEY ("approver_id") REFERENCES "users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
);

-- ----------------------------
-- Auto increment value for leave_approval_logs
-- ----------------------------
UPDATE "sqlite_sequence" SET seq = 18 WHERE name = 'leave_approval_logs';

PRAGMA foreign_keys = true;

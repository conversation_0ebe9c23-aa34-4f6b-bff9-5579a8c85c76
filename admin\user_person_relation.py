#!/usr/bin/env python3
import sqlite3
from admin.db_init import DATABASE

def init_user_person_relation_table():
    """初始化用户与人员关联表"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    # 创建用户与人员关联表
    c.execute('''CREATE TABLE IF NOT EXISTS user_person_relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                person_id INTEGER NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(user_id) REFERENCES users(id),
                FOREIGN KEY(person_id) REFERENCES persons(id),
                UNIQUE(user_id, person_id))''')
    
    conn.commit()
    conn.close()
    print("用户与人员关联表初始化完成")

def get_person_by_user_id(user_id):
    """根据用户ID获取关联的人员信息"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    c.execute("""
        SELECT p.* 
        FROM persons p
        JOIN user_person_relations upr ON p.id = upr.person_id
        WHERE upr.user_id = ?
    """, (user_id,))
    
    result = c.fetchone()
    conn.close()
    
    return dict(result) if result else None

def get_user_by_person_id(person_id):
    """根据人员ID获取关联的用户信息"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    c.execute("""
        SELECT u.* 
        FROM users u
        JOIN user_person_relations upr ON u.id = upr.user_id
        WHERE upr.person_id = ?
    """, (person_id,))
    
    result = c.fetchone()
    conn.close()
    
    return dict(result) if result else None

def create_relation(user_id, person_id):
    """创建用户与人员的关联关系"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    try:
        c.execute("INSERT INTO user_person_relations (user_id, person_id) VALUES (?, ?)",
                 (user_id, person_id))
        conn.commit()
        success = True
        message = "关联创建成功"
    except sqlite3.IntegrityError as e:
        if "UNIQUE constraint failed" in str(e):
            success = False
            message = "该关联已存在"
        else:
            success = False
            message = f"创建关联失败: {str(e)}"
    except Exception as e:
        success = False
        message = f"创建关联失败: {str(e)}"
    finally:
        conn.close()
    
    return {"success": success, "message": message}

def delete_relation(user_id, person_id):
    """删除用户与人员的关联关系"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    try:
        c.execute("DELETE FROM user_person_relations WHERE user_id = ? AND person_id = ?",
                 (user_id, person_id))
        conn.commit()
        
        if c.rowcount > 0:
            success = True
            message = "关联删除成功"
        else:
            success = False
            message = "未找到指定的关联关系"
    except Exception as e:
        success = False
        message = f"删除关联失败: {str(e)}"
    finally:
        conn.close()
    
    return {"success": success, "message": message}

def get_all_relations():
    """获取所有用户与人员的关联关系"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    c.execute("""
        SELECT upr.id, upr.user_id, upr.person_id, upr.created_at,
               u.username, p.name as person_name, p.employee_id,
               d.name as department_name
        FROM user_person_relations upr
        JOIN users u ON upr.user_id = u.id
        JOIN persons p ON upr.person_id = p.id
        LEFT JOIN departments d ON p.department_id = d.id
        ORDER BY upr.id DESC
    """)
    
    results = [dict(row) for row in c.fetchall()]
    conn.close()
    
    return results

def get_unrelated_users():
    """获取未关联人员的用户列表"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    c.execute("""
        SELECT u.id, u.username, u.role, u.email
        FROM users u
        LEFT JOIN user_person_relations upr ON u.id = upr.user_id
        WHERE upr.id IS NULL
        ORDER BY u.username
    """)
    
    results = [dict(row) for row in c.fetchall()]
    conn.close()
    
    return results

def get_unrelated_persons():
    """获取未关联用户的人员列表"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    c.execute("""
        SELECT p.id, p.name, p.employee_id, p.gender, p.position,
               d.name as department_name
        FROM persons p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN user_person_relations upr ON p.id = upr.person_id
        WHERE upr.id IS NULL
        ORDER BY p.name
    """)

    results = [dict(row) for row in c.fetchall()]
    conn.close()

    return results

def get_all_users():
    """获取所有用户列表"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    c.execute("""
        SELECT u.id, u.username, u.role, u.email, u.created_at,
               CASE WHEN upr.id IS NOT NULL THEN 1 ELSE 0 END as is_related
        FROM users u
        LEFT JOIN user_person_relations upr ON u.id = upr.user_id
        ORDER BY u.username
    """)

    results = [dict(row) for row in c.fetchall()]
    conn.close()

    return results

def get_all_persons():
    """获取所有人员列表"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    c.execute("""
        SELECT p.id, p.name, p.employee_id, p.gender, p.position, p.status,
               d.name as department_name,
               CASE WHEN upr.id IS NOT NULL THEN 1 ELSE 0 END as is_related
        FROM persons p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN user_person_relations upr ON p.id = upr.person_id
        ORDER BY p.name
    """)

    results = [dict(row) for row in c.fetchall()]
    conn.close()

    return results

def get_relation_by_id(relation_id):
    """根据关联ID获取关联信息"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    c.execute("""
        SELECT upr.id, upr.user_id, upr.person_id, upr.created_at,
               u.username, p.name as person_name, p.employee_id,
               d.name as department_name
        FROM user_person_relations upr
        JOIN users u ON upr.user_id = u.id
        JOIN persons p ON upr.person_id = p.id
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE upr.id = ?
    """, (relation_id,))

    result = c.fetchone()
    conn.close()

    return dict(result) if result else None

def check_relation_exists(user_id, person_id):
    """检查用户与人员的关联是否已存在"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()

    c.execute("SELECT id FROM user_person_relations WHERE user_id = ? AND person_id = ?",
              (user_id, person_id))
    result = c.fetchone()
    conn.close()

    return result is not None

def get_user_relations_count():
    """获取用户关联统计信息"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()

    # 总关联数
    c.execute("SELECT COUNT(*) FROM user_person_relations")
    total_relations = c.fetchone()[0]

    # 已关联用户数
    c.execute("SELECT COUNT(DISTINCT user_id) FROM user_person_relations")
    related_users = c.fetchone()[0]

    # 已关联人员数
    c.execute("SELECT COUNT(DISTINCT person_id) FROM user_person_relations")
    related_persons = c.fetchone()[0]

    # 总用户数
    c.execute("SELECT COUNT(*) FROM users")
    total_users = c.fetchone()[0]

    # 总人员数
    c.execute("SELECT COUNT(*) FROM persons")
    total_persons = c.fetchone()[0]

    conn.close()

    return {
        "total_relations": total_relations,
        "related_users": related_users,
        "related_persons": related_persons,
        "total_users": total_users,
        "total_persons": total_persons,
        "unrelated_users": total_users - related_users,
        "unrelated_persons": total_persons - related_persons
    }

if __name__ == "__main__":
    init_user_person_relation_table()

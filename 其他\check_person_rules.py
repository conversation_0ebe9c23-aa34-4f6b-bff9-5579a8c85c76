import sqlite3

conn = sqlite3.connect('face_recognition.db')
c = conn.cursor()

# 获取person_attendance_rules表的结构
c.execute("PRAGMA table_info(person_attendance_rules)")
columns = c.fetchall()
print("person_attendance_rules表结构:")
for col in columns:
    print(f"{col[0]}: {col[1]} ({col[2]})")

# 获取attendance_rules表的结构
c.execute("PRAGMA table_info(attendance_rules)")
columns = c.fetchall()
print("\nattendance_rules表结构:")
for col in columns:
    print(f"{col[0]}: {col[1]} ({col[2]})")

conn.close()

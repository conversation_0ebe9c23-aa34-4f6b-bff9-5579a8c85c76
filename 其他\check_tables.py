import sqlite3

conn = sqlite3.connect('face_recognition.db')
c = conn.cursor()
c.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = c.fetchall()
print("Tables in database:")
for table in tables:
    print(table[0])

print("\nChecking for person_attendance_rules table...")
c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='person_attendance_rules'")
if c.fetchone():
    print("person_attendance_rules table exists")
    c.execute("SELECT * FROM person_attendance_rules LIMIT 5")
    rows = c.fetchall()
    print(f"Sample data: {rows}")
else:
    print("person_attendance_rules table does not exist")

conn.close()

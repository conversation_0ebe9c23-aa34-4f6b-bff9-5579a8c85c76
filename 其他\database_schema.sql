-- 扩展现有数据库架构以支持考勤管理系统

-- 现有表结构 (已存在于系统中)
-- users表 - 系统管理员用户
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT DEFAULT 'admin',
    email TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    last_login TEXT
);

-- persons表 - 员工信息
CREATE TABLE IF NOT EXISTS persons (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    gender TEXT,
    position TEXT,
    department_id INTEGER,
    employee_id TEXT UNIQUE,
    face_image_path TEXT NOT NULL UNIQUE,
    status TEXT DEFAULT 'active', -- active, inactive, suspended
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(department_id) REFERENCES departments(id)
);

-- attendance表 - 考勤记录
CREATE TABLE IF NOT EXISTS attendance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person_id INTEGER NOT NULL,
    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
    type TEXT DEFAULT 'auto', -- auto, manual, approved
    status TEXT DEFAULT 'normal', -- normal, late, early_leave, absent
    note TEXT,
    created_by INTEGER, -- 记录创建者ID (如果是手动添加)
    FOREIGN KEY(person_id) REFERENCES persons(id),
    FOREIGN KEY(created_by) REFERENCES users(id)
);

-- 新增表结构

-- 部门表
CREATE TABLE IF NOT EXISTS departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    parent_id INTEGER,
    manager_id INTEGER,
    description TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(parent_id) REFERENCES departments(id),
    FOREIGN KEY(manager_id) REFERENCES persons(id)
);

-- 班次表
CREATE TABLE IF NOT EXISTS shifts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    start_time TEXT NOT NULL, -- 格式: HH:MM
    end_time TEXT NOT NULL, -- 格式: HH:MM
    late_threshold INTEGER DEFAULT 15, -- 迟到阈值(分钟)
    early_leave_threshold INTEGER DEFAULT 15, -- 早退阈值(分钟)
    description TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 考勤规则表
CREATE TABLE IF NOT EXISTS attendance_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 考勤规则详情表
CREATE TABLE IF NOT EXISTS rule_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_id INTEGER NOT NULL,
    day_of_week INTEGER NOT NULL, -- 0-6 (周日-周六)
    shift_id INTEGER,
    is_workday BOOLEAN DEFAULT 1,
    FOREIGN KEY(rule_id) REFERENCES attendance_rules(id),
    FOREIGN KEY(shift_id) REFERENCES shifts(id)
);

-- 部门考勤规则关联表
CREATE TABLE IF NOT EXISTS department_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    department_id INTEGER NOT NULL,
    rule_id INTEGER NOT NULL,
    effective_from TEXT NOT NULL,
    effective_to TEXT,
    FOREIGN KEY(department_id) REFERENCES departments(id),
    FOREIGN KEY(rule_id) REFERENCES attendance_rules(id)
);

-- 个人考勤规则关联表 (优先级高于部门规则)
CREATE TABLE IF NOT EXISTS person_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person_id INTEGER NOT NULL,
    rule_id INTEGER NOT NULL,
    effective_from TEXT NOT NULL,
    effective_to TEXT,
    FOREIGN KEY(person_id) REFERENCES persons(id),
    FOREIGN KEY(rule_id) REFERENCES attendance_rules(id)
);

-- 假期类型表
CREATE TABLE IF NOT EXISTS leave_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    paid BOOLEAN DEFAULT 1,
    color TEXT, -- 用于UI显示
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 假期申请表
CREATE TABLE IF NOT EXISTS leave_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person_id INTEGER NOT NULL,
    leave_type_id INTEGER NOT NULL,
    start_date TEXT NOT NULL,
    end_date TEXT NOT NULL,
    start_half INTEGER DEFAULT 0, -- 0: 全天, 1: 上午, 2: 下午
    end_half INTEGER DEFAULT 0, -- 0: 全天, 1: 上午, 2: 下午
    reason TEXT,
    status TEXT DEFAULT 'pending', -- pending, approved, rejected, cancelled
    approver_id INTEGER,
    approved_at TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(person_id) REFERENCES persons(id),
    FOREIGN KEY(leave_type_id) REFERENCES leave_types(id),
    FOREIGN KEY(approver_id) REFERENCES users(id)
);

-- 加班申请表
CREATE TABLE IF NOT EXISTS overtime_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person_id INTEGER NOT NULL,
    date TEXT NOT NULL,
    start_time TEXT NOT NULL,
    end_time TEXT NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending', -- pending, approved, rejected, cancelled
    approver_id INTEGER,
    approved_at TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(person_id) REFERENCES persons(id),
    FOREIGN KEY(approver_id) REFERENCES users(id)
);

-- 考勤修正申请表
CREATE TABLE IF NOT EXISTS attendance_corrections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person_id INTEGER NOT NULL,
    date TEXT NOT NULL,
    original_status TEXT,
    requested_status TEXT NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending', -- pending, approved, rejected, cancelled
    approver_id INTEGER,
    approved_at TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(person_id) REFERENCES persons(id),
    FOREIGN KEY(approver_id) REFERENCES users(id)
);

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT, -- info, warning, error, success
    related_to TEXT, -- 相关实体类型: leave_request, overtime, attendance, etc.
    related_id INTEGER, -- 相关实体ID
    is_read BOOLEAN DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id)
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT NOT NULL UNIQUE,
    value TEXT,
    description TEXT,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 审批流程表
CREATE TABLE IF NOT EXISTS approval_workflows (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL, -- leave, overtime, correction
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 审批步骤表
CREATE TABLE IF NOT EXISTS approval_steps (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workflow_id INTEGER NOT NULL,
    step_number INTEGER NOT NULL,
    approver_type TEXT NOT NULL, -- user, role, department_head
    approver_id INTEGER, -- 特定用户ID或角色ID
    FOREIGN KEY(workflow_id) REFERENCES approval_workflows(id)
);

-- 报表模板表
CREATE TABLE IF NOT EXISTS report_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL, -- daily, monthly, custom
    config TEXT, -- JSON格式的报表配置
    created_by INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(created_by) REFERENCES users(id)
);

-- 用户角色表
CREATE TABLE IF NOT EXISTS roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    permissions TEXT, -- JSON格式的权限列表
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    FOREIGN KEY(user_id) REFERENCES users(id),
    FOREIGN KEY(role_id) REFERENCES roles(id)
);

-- 操作日志表
CREATE TABLE IF NOT EXISTS activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    entity_type TEXT,
    entity_id INTEGER,
    details TEXT, -- JSON格式的详细信息
    ip_address TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id)
);

-- 初始数据
INSERT OR IGNORE INTO leave_types (name, description, paid, color) VALUES 
('年假', '法定带薪年假', 1, '#4CAF50'),
('病假', '因病请假', 1, '#F44336'),
('事假', '因私事请假', 0, '#2196F3'),
('婚假', '结婚休假', 1, '#9C27B0'),
('产假', '女职工产假', 1, '#E91E63'),
('陪产假', '男职工陪产假', 1, '#673AB7'),
('丧假', '直系亲属丧事', 1, '#607D8B');

-- 初始角色
INSERT OR IGNORE INTO roles (name, description, permissions) VALUES
('超级管理员', '系统最高权限', '{"all": true}'),
('人事管理员', '管理人员和考勤', '{"users": true, "attendance": true, "reports": true}'),
('部门主管', '管理部门员工和审批', '{"department": true, "approval": true}'),
('普通员工', '基本自助功能', '{"self": true}');

-- 默认班次
INSERT OR IGNORE INTO shifts (name, start_time, end_time, description) VALUES
('标准班', '09:00', '18:00', '标准8小时工作制'),
('早班', '08:00', '17:00', '早班8小时工作制'),
('晚班', '13:00', '22:00', '晚班8小时工作制');

-- 默认考勤规则
INSERT OR IGNORE INTO attendance_rules (name, description, is_default) VALUES
('标准工作日', '周一至周五工作，周末休息', 1);

-- 默认系统设置
INSERT OR IGNORE INTO settings (key, value, description) VALUES
('company_name', '公司名称', '公司或组织名称'),
('attendance_start_day', '1', '每周开始日 (0-6, 0为周日)'),
('face_recognition_threshold', '0.4', '人脸识别匹配阈值'),
('notification_enabled', 'true', '是否启用系统通知'),
('auto_approve_overtime', 'false', '是否自动批准加班申请'),
('max_continuous_work_days', '6', '最大连续工作天数');

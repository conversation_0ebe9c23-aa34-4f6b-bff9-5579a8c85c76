{% extends "admin/base.html" %}

{% block title %}{{ person.name }} - 考勤规则{% endblock %}

{% block page_title %}{{ person.name }} - 考勤规则管理{% endblock %}

{% block page_actions %}
<a href="/admin/persons" class="btn btn-secondary">
    <i class="bi bi-arrow-left"></i> 返回人员列表
</a>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">人员信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-muted">姓名:</div>
                    <div class="col-md-8">{{ person.name }}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-4 text-muted">工号:</div>
                    <div class="col-md-8">{{ person.employee_id or '未设置' }}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-4 text-muted">部门:</div>
                    <div class="col-md-8">{{ person.department_name or '未分配' }}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-4 text-muted">职位:</div>
                    <div class="col-md-8">{{ person.position or '未设置' }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">添加考勤规则</h5>
            </div>
            <div class="card-body">
                <form method="post" action="/admin/persons/{{ person.id }}/rules">
                    <div class="mb-3">
                        <label for="rule_id" class="form-label">选择规则 <span class="text-danger">*</span></label>
                        <select class="form-select" id="rule_id" name="rule_id" required>
                            <option value="">请选择考勤规则</option>
                            {% for rule in all_rules %}
                            <option value="{{ rule.id }}">{{ rule.name }} ({{ rule.check_in_time }}-{{ rule.check_out_time }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="effective_date" class="form-label">生效日期 <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="effective_date" name="effective_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">失效日期</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                        <div class="form-text">如不设置，则永久有效</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 添加规则
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title">当前考勤规则</h5>
    </div>
    <div class="card-body">
        {% if person_rules %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>规则名称</th>
                        <th>上班时间</th>
                        <th>下班时间</th>
                        <th>生效日期</th>
                        <th>失效日期</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rule in person_rules %}
                    <tr>
                        <td>{{ rule.rule_name }}</td>
                        <td>{{ rule.check_in_time }}</td>
                        <td>{{ rule.check_out_time }}</td>
                        <td>{{ rule.effective_date }}</td>
                        <td>{{ rule.expiry_date or '永久' }}</td>
                        <td>
                            {% set today = now().strftime('%Y-%m-%d') %}
                            {% if rule.effective_date <= today and (not rule.expiry_date or rule.expiry_date >= today) %}
                            <span class="badge bg-success">生效中</span>
                            {% elif rule.effective_date > today %}
                            <span class="badge bg-warning">未生效</span>
                            {% else %}
                            <span class="badge bg-secondary">已失效</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-danger delete-rule" data-id="{{ rule.id }}" data-name="{{ rule.rule_name }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            该人员尚未设置考勤规则，将使用默认规则。
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除 <span id="ruleName" class="fw-bold"></span> 规则吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" id="deleteButton" class="btn btn-danger">删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 设置默认生效日期为今天
        document.getElementById('effective_date').value = new Date().toISOString().split('T')[0];

        // 删除规则确认 - 使用事件委托处理动态元素
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const ruleNameSpan = document.getElementById('ruleName');

        // 定义全局变量保存当前选中的规则ID
        let currentRuleId = null;

        // 使用事件委托处理删除按钮点击
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('delete-rule') || e.target.closest('.delete-rule')) {
                const button = e.target.classList.contains('delete-rule') ? e.target : e.target.closest('.delete-rule');
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');

                console.log('Clicked button with id:', id, 'name:', name);

                // 保存当前选中的规则ID
                currentRuleId = id;

                ruleNameSpan.textContent = name;
                deleteModal.show();
            }
        });

        // 为删除按钮添加点击事件
        document.getElementById('deleteButton').addEventListener('click', function() {
            if (currentRuleId) {
                const url = `/admin/persons/{{ person.id }}/rules/${currentRuleId}/delete`;
                console.log('Sending DELETE request to:', url);

                // 使用fetch API发送DELETE请求
                fetch(url, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (response.ok) {
                        // 刷新页面
                        window.location.reload();
                    } else {
                        // 处理错误
                        return response.text().then(text => {
                            throw new Error(text);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败: ' + error.message);
                });

                // 关闭模态框
                deleteModal.hide();
            }
        });
    });
</script>
{% endblock %}

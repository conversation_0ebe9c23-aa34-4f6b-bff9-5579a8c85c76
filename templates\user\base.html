<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}人脸识别考勤系统 - 用户中心{% endblock %}</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #FF9800;
            --sidebar-width: 250px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            font-size: 1.1rem; /* 增加基础字体大小 */
        }

        /* Navbar */
        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            width: var(--sidebar-width);
            height: calc(100vh - 56px);
            background-color: #fff;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            z-index: 100;
            padding-top: 20px;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            color: #333;
            padding: 10px 20px;
            border-radius: 0;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover {
            background-color: #f0f0f0;
        }

        .sidebar .nav-link.active {
            background-color: #e3f2fd;
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
        }

        /* Main content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 15px;
            min-height: calc(100vh - 56px);
        }

        /* Page header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #ddd;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #eee;
            font-weight: 600;
            padding: 12px 15px;
            font-size: 1.3rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .toggle-sidebar {
                display: block !important;
            }
        }

        /* Utilities */
        .text-lg {
            font-size: 1.35rem;
        }

        /* Table styles */
        .table th {
            font-size: 1.15rem;
            font-weight: 600;
        }

        .table td {
            font-size: 1.1rem;
        }

        .bg-success-light {
            background-color: #d1e7dd;
        }

        .bg-warning-light {
            background-color: #fff3cd;
        }

        .bg-danger-light {
            background-color: #f8d7da;
        }

        .bg-info-light {
            background-color: #cff4fc;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler toggle-sidebar d-md-none" type="button">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="/user">
                <i class="bi bi-person-badge-fill me-2"></i>
                人脸识别考勤系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/" target="_blank">
                            <i class="bi bi-camera"></i> 打卡页面
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ logged_in_user }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/user/profile"><i class="bi bi-person"></i> 个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'dashboard' %}active{% endif %}" href="/user">
                    <i class="bi bi-speedometer2"></i> 仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'announcements' %}active{% endif %}" href="/user/announcements">
                    <i class="bi bi-megaphone"></i> 通知公告
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'attendance' %}active{% endif %}" href="/user/attendance">
                    <i class="bi bi-calendar-check"></i> 考勤记录
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'leave' %}active{% endif %}" href="/user/leave">
                    <i class="bi bi-calendar-x"></i> 请假管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if current_tab == 'profile' %}active{% endif %}" href="/user/profile">
                    <i class="bi bi-person"></i> 个人资料
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h3 class="mb-0">{% block page_title %}页面标题{% endblock %}</h3>
            <div>
                {% block page_actions %}{% endblock %}
            </div>
        </div>

        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 移动端侧边栏切换
        document.addEventListener('DOMContentLoaded', function() {
            const toggleSidebar = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleSidebar) {
                toggleSidebar.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // 点击主内容区域时关闭侧边栏
            document.querySelector('.main-content').addEventListener('click', function() {
                if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });

            // 初始化DataTables（避免重复初始化）
            if ($.fn.DataTable && document.querySelector('.datatable')) {
                $('.datatable').each(function() {
                    if (!$.fn.DataTable.isDataTable(this)) {
                        $(this).DataTable({
                            language: {
                                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json'
                            },
                            responsive: true
                        });
                    }
                });
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

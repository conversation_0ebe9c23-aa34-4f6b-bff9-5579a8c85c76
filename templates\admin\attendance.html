{% extends "admin/base.html" %}

{% block title %}考勤管理{% endblock %}

{% block page_title %}考勤管理{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <a href="/admin/export/attendance?start_date={{ start_date }}&end_date={{ end_date }}{% if selected_department %}&department_id={{ selected_department }}{% endif %}" class="btn btn-outline-secondary">
        <i class="bi bi-file-earmark-excel"></i> 导出Excel
    </a>
</div>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">考勤数据筛选</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3" id="filterForm">
            <div class="col-md-3">
                <label for="start_date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
            </div>
            <div class="col-md-3">
                <label for="department_id" class="form-label">部门</label>
                <select class="form-select" id="department_id" name="department_id">
                    <option value="">全部部门</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if selected_department == dept.id %}selected{% endif %}>
                        {{ dept.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i> 查询
                </button>
                <a href="/admin/attendance" class="btn btn-secondary ms-2">
                    <i class="bi bi-arrow-counterclockwise"></i> 重置
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">考勤记录</h5>
        <div class="text-muted small">
            {% if start_date and end_date %}
            时间范围: {{ start_date }} 至 {{ end_date }}
            {% endif %}
            {% if selected_department %}
            {% for dept in departments %}
            {% if dept.id == selected_department %}
            | 部门: {{ dept.name }}
            {% endif %}
            {% endfor %}
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>姓名</th>
                        <th>工号</th>
                        <th>部门</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>备注</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in records %}
                    <tr>
                        <td>{{ record.timestamp }}</td>
                        <td>{{ record.name }}</td>
                        <td>{{ record.employee_id if record.employee_id else '-' }}</td>
                        <td>{{ record.department if record.department else '-' }}</td>
                        <td>
                            {% if record.check_type == 'check_in' %}
                            <span class="badge bg-primary">上班打卡</span>
                            {% elif record.check_type == 'check_out' %}
                            <span class="badge bg-success">下班打卡</span>
                            {% elif record.check_type == 'unknown' %}
                            <span class="badge bg-secondary">未知类型</span>
                            {% else %}
                            {{ record.type if record.type else '-' }}
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if record.status == 'normal' else 'warning' if record.status in ['late','early_leave'] else 'danger' }}">
                                {% if record.status == 'normal' %}
                                正常
                                {% elif record.status == 'late' %}
                                迟到
                                {% elif record.status == 'early_leave' %}
                                早退
                                {% elif record.status == 'absent' %}
                                缺勤
                                {% else %}
                                {{ record.status }}
                                {% endif %}
                            </span>
                        </td>
                        <td>{{ record.note if record.note else '-' }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="/admin/attendance/{{ record.id }}/edit" class="btn btn-sm btn-primary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ record.id }}" data-name="{{ record.name }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除 <span id="recordName" class="fw-bold"></span> 的考勤记录吗？</p>
                <p class="text-danger">此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 初始化DataTable
        if (!$.fn.DataTable.isDataTable('.datatable')) {
            $('.datatable').DataTable({
                order: [[0, 'desc']] // 默认按时间降序排序
            });
        }

        // 表单提交前验证
        $('#filterForm').on('submit', function(e) {
            const startDate = $('#start_date').val();
            const endDate = $('#end_date').val();

            if (startDate && endDate) {
                if (new Date(startDate) > new Date(endDate)) {
                    e.preventDefault();
                    alert('开始日期不能大于结束日期');
                    return false;
                }
            }

            return true;
        });

        // 删除确认 - 使用事件委托处理分页后的元素
        $(document).on('click', '.delete-btn', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            $('#recordName').text(name);
            $('#deleteForm').attr('action', `/admin/attendance/${id}/delete`);

            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
    });
</script>
{% endblock %}
{% extends "user/base.html" %}

{% block title %}通知公告{% endblock %}

{% block page_title %}通知公告{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">公告列表</h5>
    </div>
    <div class="card-body">
        {% if announcements %}
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>标题</th>
                        <th>类型</th>
                        <th>发布时间</th>
                        <th>有效期</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for announcement in announcements %}
                    <tr>
                        <td>
                            {% if announcement.priority > 0 %}
                            <i class="bi bi-star-fill text-warning me-1" title="重要公告"></i>
                            {% endif %}
                            {{ announcement.title }}
                        </td>
                        <td>
                            {% if announcement.type == 'normal' %}
                            <span class="badge bg-primary">普通</span>
                            {% elif announcement.type == 'important' %}
                            <span class="badge bg-warning">重要</span>
                            {% elif announcement.type == 'urgent' %}
                            <span class="badge bg-danger">紧急</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ announcement.type }}</span>
                            {% endif %}
                        </td>
                        <td>{{ announcement.created_at }}</td>
                        <td>
                            {% if announcement.start_date and announcement.end_date %}
                            {{ announcement.start_date }} 至 {{ announcement.end_date }}
                            {% elif announcement.start_date %}
                            {{ announcement.start_date }} 起
                            {% elif announcement.end_date %}
                            截至 {{ announcement.end_date }}
                            {% else %}
                            永久有效
                            {% endif %}
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-primary view-btn" 
                                    data-bs-toggle="modal" data-bs-target="#announcementModal"
                                    data-title="{{ announcement.title }}" 
                                    data-content="{{ announcement.content }}"
                                    data-type="{{ announcement.type }}"
                                    data-date="{{ announcement.created_at }}">
                                <i class="bi bi-eye"></i> 查看
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-bell-slash text-muted" style="font-size: 3rem;"></i>
            <p class="mt-3">暂无公告</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 公告详情模态框 -->
<div class="modal fade" id="announcementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="announcementModalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <span class="badge" id="announcementModalType"></span>
                    <small class="text-muted ms-2" id="announcementModalDate"></small>
                </div>
                <div id="announcementModalContent" class="border-top pt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {

        // 公告详情模态框
        $('#announcementModal').on('show.bs.modal', function (event) {
            const button = $(event.relatedTarget);
            const title = button.data('title');
            const content = button.data('content');
            const type = button.data('type');
            const date = button.data('date');
            
            $('#announcementModalTitle').text(title);
            $('#announcementModalContent').html(content.replace(/\n/g, '<br>'));
            $('#announcementModalDate').text(date);
            
            // 设置类型标签样式
            const typeElement = $('#announcementModalType');
            typeElement.text(type === 'normal' ? '普通' : type === 'important' ? '重要' : type === 'urgent' ? '紧急' : type);
            
            if (type === 'normal') {
                typeElement.removeClass('bg-warning bg-danger').addClass('bg-primary');
            } else if (type === 'important') {
                typeElement.removeClass('bg-primary bg-danger').addClass('bg-warning');
            } else if (type === 'urgent') {
                typeElement.removeClass('bg-primary bg-warning').addClass('bg-danger');
            } else {
                typeElement.removeClass('bg-primary bg-warning bg-danger').addClass('bg-secondary');
            }
        });
    });
</script>
{% endblock %}

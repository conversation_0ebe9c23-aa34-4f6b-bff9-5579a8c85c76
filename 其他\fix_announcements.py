#!/usr/bin/env python3
import sqlite3
from datetime import datetime

# 数据库路径
DATABASE = "database.db"

def create_announcements_table():
    """创建通知公告表（如果不存在）"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()

    # 检查表是否存在
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='announcements'")
    if not c.fetchone():
        print("创建 announcements 表...")
        c.execute('''
            CREATE TABLE announcements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                type TEXT DEFAULT 'normal',
                priority INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active',
                start_date TEXT,
                end_date TEXT,
                created_by INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()
        print("announcements 表创建成功")
    else:
        print("announcements 表已存在")

    conn.close()

def check_announcements():
    """检查数据库中的通知公告数据"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    print("=== 所有公告 ===")
    c.execute("""
        SELECT a.id, a.title, a.content, a.type, a.priority, a.status,
               a.start_date, a.end_date, a.created_at
        FROM announcements a
        ORDER BY a.priority DESC, a.created_at DESC
    """)
    all_announcements = c.fetchall()

    if all_announcements:
        for announcement in all_announcements:
            print(f"ID: {announcement['id']}")
            print(f"标题: {announcement['title']}")
            print(f"类型: {announcement['type']}")
            print(f"状态: {announcement['status']}")
            print(f"创建时间: {announcement['created_at']}")
            print("-" * 30)
    else:
        print("没有找到任何公告")

    conn.close()

def add_test_announcement():
    """添加测试公告"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    # 添加测试公告
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    c.execute("""
        INSERT INTO announcements
        (title, content, type, priority, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, ("测试公告", "这是一条测试公告内容，用于测试公告功能是否正常工作。", "normal", 0, "active", current_time, current_time))
    
    # 添加重要公告
    c.execute("""
        INSERT INTO announcements
        (title, content, type, priority, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, ("重要通知", "这是一条重要通知，请所有员工注意查看。", "important", 1, "active", current_time, current_time))
    
    # 添加紧急公告
    c.execute("""
        INSERT INTO announcements
        (title, content, type, priority, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, ("紧急通知", "这是一条紧急通知，请立即查看并处理。", "urgent", 2, "active", current_time, current_time))
    
    conn.commit()
    conn.close()
    
    print("已添加测试公告")

def fix_announcements_display():
    """修复公告显示问题"""
    # 1. 检查公告表是否存在
    create_announcements_table()
    
    # 2. 检查是否有公告数据
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    c.execute("SELECT COUNT(*) FROM announcements")
    count = c.fetchone()[0]
    
    # 3. 如果没有公告，添加测试公告
    if count == 0:
        print("没有找到公告数据，添加测试公告...")
        conn.close()
        add_test_announcement()
    else:
        print(f"找到 {count} 条公告")
        conn.close()
    
    # 4. 检查公告数据
    check_announcements()

if __name__ == "__main__":
    fix_announcements_display()

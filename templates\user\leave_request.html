{% extends "user/base.html" %}

{% block title %}请假申请{% endblock %}

{% block page_title %}请假申请{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">新建请假申请</h5>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger">{{ error }}</div>
                {% endif %}
                {% if success %}
                <div class="alert alert-success">{{ success }}</div>
                {% endif %}
                
                <form method="post" action="/user/leave/apply" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="leave_type" class="form-label">请假类型 <span class="text-danger">*</span></label>
                        <select class="form-select" id="leave_type" name="leave_type" required>
                            <option value="">请选择请假类型</option>
                            {% for type in leave_types %}
                            <option value="{{ type.id }}" data-require-attachment="{{ type.require_attachment }}" data-max-days="{{ type.max_days }}" data-color="{{ type.color }}">
                                {{ type.name }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text" id="type_description"></div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">开始日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_time" class="form-label">开始时间</label>
                                <select class="form-select" id="start_time" name="start_time">
                                    <option value="">全天</option>
                                    <option value="09:00">上午 (09:00)</option>
                                    <option value="13:30">下午 (13:30)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">结束日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_time" class="form-label">结束时间</label>
                                <select class="form-select" id="end_time" name="end_time">
                                    <option value="">全天</option>
                                    <option value="12:00">上午 (12:00)</option>
                                    <option value="18:00">下午 (18:00)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="days" class="form-label">请假天数 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="days" name="days" step="0.5" min="0.5" required>
                        <div class="form-text" id="days_limit"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">请假原因 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3" id="attachment_group" style="display: none;">
                        <label for="attachment" class="form-label">证明材料</label>
                        <input type="file" class="form-control" id="attachment" name="attachment">
                        <div class="form-text">请上传相关证明材料（如医院证明、结婚证等）</div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">提交申请</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">请假说明</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    {% for type in leave_types %}
                    <li class="list-group-item">
                        <span class="badge" style="background-color: {{ type.color }};">{{ type.name }}</span>
                        <p class="mb-1 mt-2">{{ type.description }}</p>
                        {% if type.max_days > 0 %}
                        <small class="text-muted">最多可请 {{ type.max_days }} 天</small>
                        {% endif %}
                        {% if type.require_attachment == 1 %}
                        <br><small class="text-danger">需要提供证明材料</small>
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const leaveTypeSelect = document.getElementById('leave_type');
        const attachmentGroup = document.getElementById('attachment_group');
        const typeDescription = document.getElementById('type_description');
        const daysLimit = document.getElementById('days_limit');
        const daysInput = document.getElementById('days');
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        
        // 设置默认日期
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        startDateInput.valueAsDate = today;
        endDateInput.valueAsDate = tomorrow;
        
        // 计算请假天数
        function calculateDays() {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);
            
            if (startDate && endDate && startDate <= endDate) {
                // 计算工作日天数
                let days = 0;
                const currentDate = new Date(startDate);
                
                while (currentDate <= endDate) {
                    const dayOfWeek = currentDate.getDay();
                    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // 排除周六日
                        days++;
                    }
                    currentDate.setDate(currentDate.getDate() + 1);
                }
                
                // 处理半天请假
                const startTime = document.getElementById('start_time').value;
                const endTime = document.getElementById('end_time').value;
                
                if (days === 1 && (startTime || endTime)) {
                    days = 0.5;
                } else if (days > 1 && startTime && endTime) {
                    days -= 0.5;
                }
                
                daysInput.value = days;
            }
        }
        
        // 请假类型变更事件
        leaveTypeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const requireAttachment = selectedOption.getAttribute('data-require-attachment') === '1';
            const maxDays = selectedOption.getAttribute('data-max-days');
            const description = selectedOption.textContent.trim();
            const color = selectedOption.getAttribute('data-color');
            
            // 显示或隐藏附件上传
            attachmentGroup.style.display = requireAttachment ? 'block' : 'none';
            
            // 显示请假类型描述
            if (this.value) {
                typeDescription.innerHTML = `<span class="badge" style="background-color: ${color};">${description}</span>`;
                
                if (maxDays > 0) {
                    daysLimit.textContent = `该类型请假最多可请 ${maxDays} 天`;
                    daysInput.setAttribute('max', maxDays);
                } else {
                    daysLimit.textContent = '';
                    daysInput.removeAttribute('max');
                }
            } else {
                typeDescription.textContent = '';
                daysLimit.textContent = '';
                daysInput.removeAttribute('max');
            }
        });
        
        // 日期变更事件
        startDateInput.addEventListener('change', calculateDays);
        endDateInput.addEventListener('change', calculateDays);
        document.getElementById('start_time').addEventListener('change', calculateDays);
        document.getElementById('end_time').addEventListener('change', calculateDays);
        
        // 初始计算天数
        calculateDays();
    });
</script>
{% endblock %}

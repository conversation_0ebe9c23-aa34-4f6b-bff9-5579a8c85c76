#!/usr/bin/env python3
import sqlite3
import os

# 数据库路径
DATABASE_FILES = ["database.db", "attendance.db", "face_recognition.db"]

def check_attendance_records():
    """
    检查考勤记录的状态
    """
    print("开始检查考勤记录...")
    
    # 尝试每个数据库文件
    for db_file in DATABASE_FILES:
        if not os.path.exists(db_file):
            print(f"数据库文件 {db_file} 不存在，跳过")
            continue
            
        print(f"检查数据库: {db_file}")
        
        # 连接数据库
        conn = sqlite3.connect(db_file)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # 检查是否存在attendance表
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='attendance'")
        if not c.fetchone():
            print(f"数据库 {db_file} 中没有attendance表，跳过")
            conn.close()
            continue
            
        print(f"在数据库 {db_file} 中找到attendance表，开始检查...")
        
        try:
            # 获取表结构
            c.execute("PRAGMA table_info(attendance)")
            columns = [col['name'] for col in c.fetchall()]
            print(f"attendance表的列: {', '.join(columns)}")
            
            # 检查是否有check_type列
            if 'check_type' not in columns:
                print("attendance表中没有check_type列")
            else:
                # 获取check_type的统计信息
                c.execute("""
                    SELECT check_type, COUNT(*) as count
                    FROM attendance
                    GROUP BY check_type
                """)
                type_counts = c.fetchall()
                
                print(f"\n=== 数据库 {db_file} 中的考勤类型统计 ===")
                for type_count in type_counts:
                    check_type = type_count['check_type'] if type_count['check_type'] else 'NULL'
                    print(f"{check_type}: {type_count['count']} 条记录")
            
            # 检查是否有status列
            if 'status' not in columns:
                print("attendance表中没有status列")
            else:
                # 获取status的统计信息
                c.execute("""
                    SELECT status, COUNT(*) as count
                    FROM attendance
                    GROUP BY status
                """)
                status_counts = c.fetchall()
                
                print(f"\n=== 数据库 {db_file} 中的考勤状态统计 ===")
                for status_count in status_counts:
                    status = status_count['status'] if status_count['status'] else 'NULL'
                    print(f"{status}: {status_count['count']} 条记录")
            
            # 获取总记录数
            c.execute("SELECT COUNT(*) as count FROM attendance")
            total_count = c.fetchone()['count']
            print(f"\n总记录数: {total_count}")
            
            # 获取最近10条记录
            c.execute("""
                SELECT id, person_id, timestamp, check_type, status, type
                FROM attendance
                ORDER BY id DESC
                LIMIT 10
            """)
            recent_records = c.fetchall()
            
            print("\n最近10条记录:")
            for record in recent_records:
                record_dict = dict(record)
                print(record_dict)
                
        except Exception as e:
            print(f"检查数据库 {db_file} 中的考勤记录时出错: {str(e)}")
        finally:
            conn.close()

if __name__ == "__main__":
    check_attendance_records()

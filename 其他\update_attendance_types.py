#!/usr/bin/env python3
import sqlite3
import os
from datetime import datetime

# 数据库路径
DATABASE_FILES = ["database.db", "attendance.db", "face_recognition.db"]

def update_attendance_types():
    """
    更新考勤记录中的类型字段，将未知类型根据时间更新为上班打卡或下班打卡
    """
    print("开始更新考勤记录类型...")

    # 尝试每个数据库文件
    for db_file in DATABASE_FILES:
        if not os.path.exists(db_file):
            print(f"数据库文件 {db_file} 不存在，跳过")
            continue

        print(f"检查数据库: {db_file}")

        # 连接数据库
        conn = sqlite3.connect(db_file)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # 检查是否存在attendance表
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='attendance'")
        if not c.fetchone():
            print(f"数据库 {db_file} 中没有attendance表，跳过")
            conn.close()
            continue

        print(f"在数据库 {db_file} 中找到attendance表，开始处理...")

        try:
            # 获取所有考勤记录，检查check_type和type字段
            c.execute("""
                SELECT id, timestamp, check_type, type
                FROM attendance
            """)
            records = c.fetchall()

            if not records:
                print(f"数据库 {db_file} 中没有找到考勤记录")
                conn.close()
                continue

            print(f"在数据库 {db_file} 中找到 {len(records)} 条考勤记录")

            # 更新每条记录
            updated_count = 0
            for record in records:
                record_id = record['id']
                timestamp = record['timestamp']
                current_check_type = record['check_type']
                current_type = record['type']

                # 如果check_type不是check_in或check_out，但type是，则更新check_type
                if (current_check_type != 'check_in' and current_check_type != 'check_out') and \
                   (current_type == 'check_in' or current_type == 'check_out'):
                    try:
                        # 更新记录，使check_type与type一致
                        c.execute("""
                            UPDATE attendance
                            SET check_type = ?
                            WHERE id = ?
                        """, (current_type, record_id))

                        updated_count += 1

                    except Exception as e:
                        print(f"更新记录 {record_id} 时出错: {str(e)}")
                        continue

                # 如果check_type和type都不是check_in或check_out，则根据时间判断
                elif (current_check_type != 'check_in' and current_check_type != 'check_out') and \
                     (current_type != 'check_in' and current_type != 'check_out'):
                    try:
                        # 解析时间戳
                        dt = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
                        hour = dt.hour

                        # 根据时间判断打卡类型
                        # 上午12点前为上班打卡，下午12点后为下班打卡
                        if hour < 12:
                            new_check_type = 'check_in'
                        else:
                            new_check_type = 'check_out'

                        # 更新记录
                        c.execute("""
                            UPDATE attendance
                            SET check_type = ?, type = ?
                            WHERE id = ?
                        """, (new_check_type, new_check_type, record_id))

                        updated_count += 1

                    except Exception as e:
                        print(f"更新记录 {record_id} 时出错: {str(e)}")

            # 提交更改
            conn.commit()
            print(f"成功更新数据库 {db_file} 中的 {updated_count} 条考勤记录的类型")

            # 验证更新结果
            c.execute("""
                SELECT check_type, COUNT(*) as count
                FROM attendance
                GROUP BY check_type
            """)
            type_counts = c.fetchall()

            print(f"\n=== 数据库 {db_file} 更新后的考勤类型统计 ===")
            for type_count in type_counts:
                print(f"{type_count['check_type'] or 'NULL'}: {type_count['count']} 条记录")

        except Exception as e:
            conn.rollback()
            print(f"更新数据库 {db_file} 中的考勤记录类型时出错: {str(e)}")
        finally:
            conn.close()

if __name__ == "__main__":
    update_attendance_types()
